#this app.prop  are for local running
# in your IDE add VM options:
spring.application.name=UAV-Docking-Management-System
spring.datasource.url=*****************************************************
spring.datasource.username=root
spring.datasource.password=younes123
spring.jpa.hibernate.ddl-auto=update
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
# Enable debug logging for Spring MVC resource handling
logging.level.org.springframework.web=DEBUG

# Static resources configuration
spring.mvc.static-path-pattern=/resources/**
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.add-mappings=true


