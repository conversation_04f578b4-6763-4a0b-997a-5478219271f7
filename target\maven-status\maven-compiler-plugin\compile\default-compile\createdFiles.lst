com\example\uavdockingmanagementsystem\config\GlobalExceptionHandler$UAVBusinessException.class
com\example\uavdockingmanagementsystem\controller\RegionController.class
com\example\uavdockingmanagementsystem\model\SecurityEvent.class
com\example\uavdockingmanagementsystem\model\SecurityEvent$SecurityEventType.class
com\example\uavdockingmanagementsystem\repository\BatteryStatusRepository.class
com\example\uavdockingmanagementsystem\config\RateLimitingConfig$RateLimitInfo.class
com\example\uavdockingmanagementsystem\config\CacheConfig.class
com\example\uavdockingmanagementsystem\controller\AccessControlAPI.class
com\example\uavdockingmanagementsystem\repository\FlightLogRepository.class
com\example\uavdockingmanagementsystem\model\FlightLog$FlightStatus.class
com\example\uavdockingmanagementsystem\config\GlobalExceptionHandler.class
com\example\uavdockingmanagementsystem\config\RateLimitingConfig$RateLimitService.class
com\example\uavdockingmanagementsystem\model\Notification$NotificationType.class
com\example\uavdockingmanagementsystem\model\AuditLog$AuditAction.class
com\example\uavdockingmanagementsystem\model\BatteryStatus.class
com\example\uavdockingmanagementsystem\model\SecurityEvent$Severity.class
com\example\uavdockingmanagementsystem\model\MaintenanceRecord$Priority.class
com\example\uavdockingmanagementsystem\repository\RegionRepository.class
com\example\uavdockingmanagementsystem\controller\UAVController.class
com\example\uavdockingmanagementsystem\UavDockingManagementSystemApplication.class
com\example\uavdockingmanagementsystem\model\Region.class
com\example\uavdockingmanagementsystem\repository\MaintenanceRecordRepository.class
com\example\uavdockingmanagementsystem\controller\HibernatePodController.class
com\example\uavdockingmanagementsystem\service\EmailNotificationService.class
com\example\uavdockingmanagementsystem\model\AuditLog.class
com\example\uavdockingmanagementsystem\controller\AnalyticsDashboardController.class
com\example\uavdockingmanagementsystem\model\MaintenanceRecord.class
com\example\uavdockingmanagementsystem\model\UAV$OperationalStatus.class
com\example\uavdockingmanagementsystem\config\RateLimitingConfig.class
com\example\uavdockingmanagementsystem\config\WebSocketConfig.class
com\example\uavdockingmanagementsystem\config\WebConfig.class
com\example\uavdockingmanagementsystem\controller\UAVRestController.class
com\example\uavdockingmanagementsystem\model\AuditLog$AuditResult.class
com\example\uavdockingmanagementsystem\dto\UAVStatusResponse.class
com\example\uavdockingmanagementsystem\repository\UAVRepository.class
com\example\uavdockingmanagementsystem\model\UAV.class
com\example\uavdockingmanagementsystem\service\UAVService.class
com\example\uavdockingmanagementsystem\service\RealTimeMonitoringService.class
com\example\uavdockingmanagementsystem\service\RealTimeSimulationService$1.class
com\example\uavdockingmanagementsystem\config\RateLimitingConfig$InMemoryRateLimitService.class
com\example\uavdockingmanagementsystem\model\MaintenanceRecord$MaintenanceType.class
com\example\uavdockingmanagementsystem\model\FlightLog.class
com\example\uavdockingmanagementsystem\controller\HealthCheckController.class
com\example\uavdockingmanagementsystem\model\Notification$Priority.class
com\example\uavdockingmanagementsystem\model\BatteryStatus$ChargingStatus.class
com\example\uavdockingmanagementsystem\model\ApiKey$ApiPermission.class
com\example\uavdockingmanagementsystem\config\SecurityConfig.class
com\example\uavdockingmanagementsystem\interceptor\RateLimitInterceptor$RateLimitConfig.class
com\example\uavdockingmanagementsystem\model\Notification$NotificationStatus.class
com\example\uavdockingmanagementsystem\service\FlightLogService$FlightStatistics.class
com\example\uavdockingmanagementsystem\model\AuditLog$EventType.class
com\example\uavdockingmanagementsystem\model\ApiKey.class
com\example\uavdockingmanagementsystem\config\RateLimitingConfig$RateLimitBucket.class
com\example\uavdockingmanagementsystem\dto\UAVStatusResponse$UAVSummary.class
com\example\uavdockingmanagementsystem\model\ApiKey$ApiKeyStatus.class
com\example\uavdockingmanagementsystem\model\UAV$Status.class
com\example\uavdockingmanagementsystem\service\FlightLogService.class
com\example\uavdockingmanagementsystem\model\MaintenanceRecord$MaintenanceStatus.class
com\example\uavdockingmanagementsystem\model\HibernatePod.class
com\example\uavdockingmanagementsystem\interceptor\RateLimitInterceptor.class
com\example\uavdockingmanagementsystem\model\BatteryStatus$BatteryCondition.class
com\example\uavdockingmanagementsystem\model\AuditLog$Severity.class
com\example\uavdockingmanagementsystem\model\Notification.class
com\example\uavdockingmanagementsystem\service\RegionService.class
