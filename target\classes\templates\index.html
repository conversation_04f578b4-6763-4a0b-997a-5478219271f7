<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UAV Docking Management System</title>
    <link rel="stylesheet" type="text/css" href="/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- Ensure Font Awesome is loaded properly -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" integrity="sha512-fD9DI5bZwQxOi7MhYWnnNPlvXdp/2Pj3XSTRrFs5FQa4mizyGLnJcN6tuvUS6LbmgN1ut+XGSABKvjN0H6Aoow==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <!-- Performance optimization -->
    <script src="/js/performance.js"></script>
    <!-- Enhanced JavaScript functionality -->
    <script src="/js/app.js" defer></script>
    <!-- Development tests (remove in production) -->
    <script src="/js/tests.js" defer></script>
    <style>
        /* Ensure proper icon display */
        .fas {
            display: inline-block !important;
            width: auto !important;
            height: auto !important;
            vertical-align: middle;
        }
        .form-icon .fas {
            font-size: 1rem;
        }
        /* Add a fallback for missing drone icon */
        .fa-drone:before {
            content: "\f72e"; /* Using helicopter icon as fallback */
        }
    </style>
    </head>
    <body>
    <div class="container">
    <header>
        <h1><i class="fas fa-plane" aria-hidden="true"></i> UAV Docking Management System</h1>
    </header>

    <!-- System Status Dashboard -->
    <section class="status-dashboard" role="region" aria-label="System Status Dashboard">
        <div class="status-card" role="status" aria-label="Hibernate Pod Status">
            <div class="status-icon" aria-hidden="true">
                <i class="fas fa-bed"></i>
            </div>
            <div class="status-info">
                <h3>Hibernate Pod</h3>
                <p class="status-value" th:text="${hibernatePod?.currentCapacity ?: 0} + ' / ' + ${hibernatePod?.maxCapacity ?: 5}"
                   th:attr="aria-label='Hibernate pod capacity: ' + ${hibernatePod?.currentCapacity ?: 0} + ' out of ' + ${hibernatePod?.maxCapacity ?: 5} + ' slots used'">0 / 5</p>
                <span class="status-label" th:classappend="${hibernatePodFull} ? 'full' : 'available'">
                    <span th:if="${hibernatePodFull}">Full</span>
                    <span th:unless="${hibernatePodFull}">Available</span>
                </span>
            </div>
        </div>
        <div class="status-card">
            <div class="status-icon">
                <i class="fas fa-helicopter"></i>
            </div>
            <div class="status-info">
                <h3>Total UAVs</h3>
                <p class="status-value" th:text="${#lists.size(uavs)}">0</p>
                <span class="status-label">Registered</span>
            </div>
        </div>
        <div class="status-card">
            <div class="status-icon authorized">
                <i class="fas fa-check-shield"></i>
            </div>
            <div class="status-info">
                <h3>Authorized</h3>
                <p class="status-value" th:text="${#lists.size(#lists.select(uavs, uav.status.name() == 'AUTHORIZED'))}">0</p>
                <span class="status-label available">Active</span>
            </div>
        </div>
        <div class="status-card">
            <div class="status-icon">
                <i class="fas fa-map-marker-alt"></i>
            </div>
            <div class="status-info">
                <h3>Regions</h3>
                <p class="status-value" th:text="${#lists.size(allRegions)}">0</p>
                <span class="status-label">Available</span>
            </div>
        </div>
    </div>

    <section role="region" aria-labelledby="registration-heading">
        <h2 id="registration-heading"><i class="fas fa-plus-circle" aria-hidden="true"></i> Register New UAV</h2>

        <div class="form-card hover-card">
            <div class="form-card-header">
                <i class="fas fa-helicopter" aria-hidden="true"></i> Quick UAV Registration
            </div>
            <div class="form-card-body">
                <form action="/uav/add" method="POST" th:action="@{/uav/add}"
                      role="form" aria-label="UAV Registration Form" novalidate>
                <div class="form-row">
                    <div class="form-col">
                        <label for="rfidTag" class="required">RFID Tag</label>
                        <div class="form-icon">
                            <i class="fas fa-tag"></i>
                        </div>
                        <input type="text" id="rfidTag" name="rfidTag" required
                               placeholder="Enter RFID tag"
                               minlength="3"
                               maxlength="50"
                               pattern="[A-Za-z0-9-_]+"
                               title="RFID tag should contain only letters, numbers, hyphens, and underscores">
                    </div>

                    <div class="form-col">
                        <label for="ownerName" class="required">Owner Name</label>
                        <div class="form-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <input type="text" id="ownerName" name="ownerName" required
                               placeholder="Enter owner's name"
                               minlength="2"
                               maxlength="100"
                               pattern="[A-Za-z\s]+"
                               title="Owner name should contain only letters and spaces">
                    </div>

                    <div class="form-col">
                        <label for="model" class="required">Model</label>
                        <div class="form-icon">
                            <i class="fas fa-helicopter"></i>
                        </div>
                        <input type="text" id="model" name="model" required
                               placeholder="Enter UAV model"
                               minlength="2"
                               maxlength="100">
                    </div>
                    <!-- Enhanced hibernate pod selection -->
                    <div class="form-col">
                        <label for="inHibernatePod" class="required">Join Hibernate Pod</label>
                        <div class="form-icon">
                            <i class="fas fa-bed"></i>
                        </div>
                        <select name="inHibernatePod" id="inHibernatePod" required th:disabled="${hibernatePodFull}">
                            <option value="">-- Select Option --</option>
                            <option value="true" th:disabled="${hibernatePodFull}">
                                Yes <span th:if="${hibernatePodFull}">(Pod Full)</span>
                            </option>
                            <option value="false" selected>No</option>
                        </select>
                        <div th:if="${hibernatePodFull}" class="field-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Hibernate pod is currently full. Cannot add new UAVs.
                        </div>
                    </div>

                    <div class="form-col">
                        <label for="status" class="required">Status</label>
                        <div class="form-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <select name="status" id="status" required>
                            <option value="AUTHORIZED">Authorized</option>
                            <option value="UNAUTHORIZED">Unauthorized</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-col" style="flex: 3;">
                        <label for="regionIds">Regions (Hold Ctrl/Cmd to select multiple)</label>
                        <div class="form-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <select name="regionIds" id="regionIds" multiple style="height: 70px;">
                            <option th:if="${allRegions == null || #lists.isEmpty(allRegions)}" disabled>No regions available</option>
                            <option th:unless="${allRegions == null || #lists.isEmpty(allRegions)}" 
                                    th:each="region : ${allRegions}" 
                                    th:value="${region.id}" 
                                    th:text="${region.regionName}"></option>
                        </select>
                    </div>
                    
                    <div class="form-col" style="flex: 1; display: flex; align-items: flex-end;">
                        <button type="submit" style="width: 100%; margin-bottom: 0;" id="submitBtn">
                            <i class="fas fa-plus"></i> Register UAV
                        </button>
                    </div>
                </div>

                <!-- Form Progress Indicator -->
                <div class="form-progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <p class="progress-text">Processing UAV registration...</p>
                </div>
            </form>
        </div>
    </div>

    </section>

    <section role="region" aria-labelledby="uav-list-heading">
        <h2 id="uav-list-heading"><i class="fas fa-list" aria-hidden="true"></i> UAV List</h2>
        <div class="table-container">
            <div class="table-controls">
                <div class="search-box">
                    <label for="tableSearch" class="sr-only">Search UAVs</label>
                    <i class="fas fa-search" aria-hidden="true"></i>
                    <input type="text" id="tableSearch" placeholder="Search UAVs..."
                           aria-describedby="search-help" />
                    <div id="search-help" class="sr-only">
                        Search by RFID tag, owner name, model, or status
                    </div>
                </div>
                <div class="table-info">
                    <span id="tableCount" th:text="'Total: ' + ${#lists.size(uavs)} + ' UAVs'"
                          role="status" aria-live="polite">Total: 0 UAVs</span>
                </div>
            </div>
            <table role="table" aria-label="UAV Management Table">
                <thead>
                <tr role="row">
                    <th class="sortable" data-column="id" role="columnheader"
                        aria-sort="none" tabindex="0">
                        <i class="fas fa-id-card" aria-hidden="true"></i> ID
                    </th>
                    <th class="sortable" data-column="rfidTag" role="columnheader"
                        aria-sort="none" tabindex="0">
                        <i class="fas fa-tag" aria-hidden="true"></i> RFID Tag
                    </th>
                    <th class="sortable" data-column="ownerName" role="columnheader"
                        aria-sort="none" tabindex="0">
                        <i class="fas fa-user" aria-hidden="true"></i> Owner Name
                    </th>
                    <th class="sortable" data-column="model" role="columnheader"
                        aria-sort="none" tabindex="0">
                        <i class="fas fa-helicopter" aria-hidden="true"></i> Model
                    </th>
                    <th class="sortable" data-column="status" role="columnheader"
                        aria-sort="none" tabindex="0">
                        <i class="fas fa-shield-alt" aria-hidden="true"></i> Status
                    </th>
                    <th role="columnheader">
                        <i class="fas fa-map-marker-alt" aria-hidden="true"></i> Regions
                    </th>
                    <th class="sortable" data-column="createdAt" role="columnheader"
                        aria-sort="none" tabindex="0">
                        <i class="fas fa-calendar-plus" aria-hidden="true"></i> Created At
                    </th>
                    <th class="sortable" data-column="updatedAt" role="columnheader"
                        aria-sort="none" tabindex="0">
                        <i class="fas fa-calendar-check" aria-hidden="true"></i> Updated At
                    </th>
                    <th class="sortable" data-column="hibernatePod" role="columnheader"
                        aria-sort="none" tabindex="0">
                        <i class="fas fa-bed" aria-hidden="true"></i> Hibernate Pod
                    </th>
                    <th role="columnheader">
                        <i class="fas fa-tools" aria-hidden="true"></i> Actions
                    </th>
                </tr>
                </thead>
            <tbody>
            <tr th:each="uav : ${uavs}" role="row">
                <td role="cell" th:text="${uav.id}"></td>
                <td role="cell" th:text="${uav.rfidTag}"></td>
                <td role="cell" th:text="${uav.ownerName}"></td>
                <td role="cell" th:text="${uav.model}"></td>
                <td role="cell">
                    <span th:text="${uav.status}"
                          th:class="'status-pill ' + ${uav.status == T(com.example.uavdockingmanagementsystem.model.UAV.Status).AUTHORIZED ? 'authorized' : 'unauthorized'}"
                          th:attr="aria-label='Status: ' + ${uav.status}">
                    </span>
                </td>
                <td class="regions">
                    <span th:if="${uav.regions == null || #lists.isEmpty(uav.regions)}">No regions assigned</span>
                    <span th:unless="${uav.regions == null || #lists.isEmpty(uav.regions)}">
                        <span th:each="region, iterStat : ${uav.regions}">
                            <span th:text="${region.regionName}"></span>
                            <a th:href="@{/uav/{uavId}/remove-region/{regionId}(uavId=${uav.id},regionId=${region.id})}" 
                               class="btn-remove"><i class="fas fa-times"></i></a>
                            <span th:if="${!iterStat.last}">, </span>
                        </span>
                    </span>
                    <a th:href="@{/uav/{id}/add-region(id=${uav.id})}" 
                       class="btn-add"><i class="fas fa-plus"></i></a>
                </td>
                <td th:text="${#temporals.format(uav.createdAt, 'yyyy-MM-dd HH:mm:ss')}"></td>
                <td th:text="${#temporals.format(uav.updatedAt, 'yyyy-MM-dd HH:mm:ss')}"></td>
                <td>
                    <span th:if="${uav.inHibernatePod}">Yes</span>
                    <span th:unless="${uav.inHibernatePod}">No</span>
                </td>
                <td role="cell">
                    <a th:href="@{/uav/update-status/{id}(id=${uav.id})}"
                       class="action-btn btn-toggle"
                       th:attr="aria-label='Toggle authorization status for UAV ' + ${uav.rfidTag}"
                       role="button">
                        <i class="fas fa-exchange-alt" aria-hidden="true"></i>
                        <span class="sr-only">Toggle Status</span>
                        Toggle
                    </a>

                    <!-- Hibernate Pod Actions -->
                    <button th:if="${!uav.inHibernatePod}"
                            class="action-btn btn-hibernate-add"
                            th:attr="data-uav-id=${uav.id}, aria-label='Add UAV ' + ${uav.rfidTag} + ' to hibernate pod'"
                            onclick="addToHibernatePod(this.dataset.uavId)"
                            role="button">
                        <i class="fas fa-bed" aria-hidden="true"></i>
                        <span class="sr-only">Add to Hibernate Pod</span>
                        Hibernate
                    </button>

                    <button th:if="${uav.inHibernatePod}"
                            class="action-btn btn-hibernate-remove"
                            th:attr="data-uav-id=${uav.id}, aria-label='Remove UAV ' + ${uav.rfidTag} + ' from hibernate pod'"
                            onclick="removeFromHibernatePod(this.dataset.uavId)"
                            role="button">
                        <i class="fas fa-bed" aria-hidden="true"></i>
                        <span class="sr-only">Remove from Hibernate Pod</span>
                        Wake Up
                    </button>

                    <a th:href="@{/uav/delete/{id}(id=${uav.id})}"
                       class="action-btn btn-delete"
                       th:attr="aria-label='Delete UAV ' + ${uav.rfidTag}"
                       role="button">
                        <i class="fas fa-trash" aria-hidden="true"></i>
                        <span class="sr-only">Delete UAV</span>
                        Delete
                    </a>
                </td>
            </tr>
            </tbody>
        </table>
        </div>
    </section>

    <footer role="contentinfo">
        <p>&copy; 2025 UAV Docking Management System |
           <i class="fas fa-code" aria-hidden="true"></i> Made with
           <i class="fas fa-heart" style="color:#e74c3c;" aria-label="love"></i>
        </p>
        <p class="accessibility-info">
            <small>This application supports keyboard navigation and screen readers.
                   Press Tab to navigate, Enter to activate buttons, and Escape to close dialogs.
            </small>
        </p>
    </footer>
</div>

<script>
    // Add simple animation to the header
    document.addEventListener('DOMContentLoaded', function() {
        const header = document.querySelector('h1');
        header.classList.add('pulse');
        
        // Add hover effects to table rows
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.01)';
                this.style.transition = 'transform 0.2s';
                this.style.backgroundColor = 'rgba(52, 152, 219, 0.05)';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.backgroundColor = '';
            });
        });
    });

    // Hibernate Pod Management Functions
    async function addToHibernatePod(uavId) {
        const button = document.querySelector(`[data-uav-id="${uavId}"].btn-hibernate-add`);
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
        }

        try {
            const response = await fetch('/api/hibernate-pod/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `uavId=${uavId}`
            });

            const result = await response.json();

            if (result.success) {
                showMessage(result.message, 'success');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showMessage(result.message, 'error');
                if (button) {
                    button.disabled = false;
                    button.innerHTML = '<i class="fas fa-bed"></i> Hibernate';
                }
            }
        } catch (error) {
            console.error('Error adding UAV to hibernate pod:', error);
            showMessage('Failed to add UAV to hibernate pod', 'error');
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-bed"></i> Hibernate';
            }
        }
    }

    async function removeFromHibernatePod(uavId) {
        const button = document.querySelector(`[data-uav-id="${uavId}"].btn-hibernate-remove`);
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Removing...';
        }

        try {
            const response = await fetch('/api/hibernate-pod/remove', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `uavId=${uavId}`
            });

            const result = await response.json();

            if (result.success) {
                showMessage(result.message, 'success');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showMessage(result.message, 'error');
                if (button) {
                    button.disabled = false;
                    button.innerHTML = '<i class="fas fa-bed"></i> Wake Up';
                }
            }
        } catch (error) {
            console.error('Error removing UAV from hibernate pod:', error);
            showMessage('Failed to remove UAV from hibernate pod', 'error');
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-bed"></i> Wake Up';
            }
        }
    }

    function showMessage(message, type) {
        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type}`;
        messageDiv.innerHTML = `
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'}"></i>
            ${message}
        `;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            background: ${type === 'success' ? '#d4edda' : '#f8d7da'};
            color: ${type === 'success' ? '#155724' : '#721c24'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : '#f5c6cb'};
        `;

        document.body.appendChild(messageDiv);

        // Remove message after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 5000);
    }
</script>
</body>
</html>