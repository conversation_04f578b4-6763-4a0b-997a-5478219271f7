<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error - UAV Docking Management System</title>
    <link rel="stylesheet" type="text/css" href="/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .error-container {
            max-width: 600px;
            margin: 2rem auto;
            padding: 2rem;
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .error-icon {
            font-size: 4rem;
            color: #e74c3c;
            margin-bottom: 1rem;
        }
        
        .error-title {
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .error-message {
            color: #7f8c8d;
            margin-bottom: 2rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #e74c3c;
        }
        
        .error-details {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: left;
            font-family: monospace;
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #3498db, #2c3e50);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: transform 0.2s;
        }
        
        .back-button:hover {
            transform: translateY(-2px);
            text-decoration: none;
            color: white;
        }
        
        .help-section {
            margin-top: 2rem;
            padding: 1rem;
            background-color: #e8f4fd;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .help-section h4 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .help-section p {
            color: #5a6c7d;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-container">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <h1 class="error-title">Oops! Something went wrong</h1>
            
            <div class="error-message" th:if="${message}">
                <strong>Error:</strong> <span th:text="${message}">An unexpected error occurred</span>
            </div>
            
            <div class="error-details" th:if="${exception}">
                <strong>Exception Type:</strong> <span th:text="${exception}">Unknown</span><br>
                <strong>Timestamp:</strong> <span th:text="${#dates.format(#dates.createNow(), 'yyyy-MM-dd HH:mm:ss')}"></span>
            </div>
            
            <a href="/uav/" class="back-button">
                <i class="fas fa-arrow-left"></i>
                Back to UAV Management
            </a>
            
            <div class="help-section">
                <h4><i class="fas fa-info-circle"></i> What can you do?</h4>
                <p>
                    • Try refreshing the page<br>
                    • Go back to the main page and try again<br>
                    • If the problem persists, please contact the system administrator
                </p>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-redirect to main page after 30 seconds
        setTimeout(() => {
            if (confirm('Would you like to return to the main page?')) {
                window.location.href = '/uav/';
            }
        }, 30000);
        
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            const errorContainer = document.querySelector('.error-container');
            errorContainer.style.opacity = '0';
            errorContainer.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                errorContainer.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                errorContainer.style.opacity = '1';
                errorContainer.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
