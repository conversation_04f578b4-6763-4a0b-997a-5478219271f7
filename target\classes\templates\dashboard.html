<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-Time Dashboard - UAV Management System</title>
    <link rel="stylesheet" type="text/css" href="/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sockjs-client/1.6.1/sockjs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/stomp.js/2.3.3/stomp.min.js"></script>
    <style>
        .dashboard-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .dashboard-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-icon {
            font-size: 1.5rem;
            color: #3498db;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin: 10px 0;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }
        .status-offline { background-color: #95a5a6; }

        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 20px;
        }

        .alert-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .alert-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .alert-critical { 
            background-color: #fdf2f2; 
            border-left-color: #e74c3c; 
        }

        .alert-warning { 
            background-color: #fefbf3; 
            border-left-color: #f39c12; 
        }

        .alert-info { 
            background-color: #f0f8ff; 
            border-left-color: #3498db; 
        }

        .uav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .uav-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .uav-card:hover {
            background: #e9ecef;
            transform: scale(1.02);
        }

        .uav-status {
            font-weight: 600;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            text-transform: uppercase;
        }

        .status-authorized {
            background-color: #d4edda;
            color: #155724;
        }

        .status-unauthorized {
            background-color: #f8d7da;
            color: #721c24;
        }

        .battery-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px 0;
        }

        .battery-bar {
            width: 60px;
            height: 20px;
            border: 2px solid #ddd;
            border-radius: 4px;
            position: relative;
            margin-right: 10px;
        }

        .battery-fill {
            height: 100%;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .battery-high { background-color: #27ae60; }
        .battery-medium { background-color: #f39c12; }
        .battery-low { background-color: #e74c3c; }

        .last-updated {
            font-size: 0.8rem;
            color: #6c757d;
            text-align: center;
            margin-top: 20px;
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 25px;
            color: white;
            font-weight: 600;
            z-index: 1000;
        }

        .connected { background-color: #27ae60; }
        .disconnected { background-color: #e74c3c; }
        .connecting { background-color: #f39c12; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .notification-toast {
            position: fixed;
            top: 80px;
            right: 20px;
            max-width: 400px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 15px;
            z-index: 1001;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification-toast.show {
            transform: translateX(0);
        }

        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .notification-title {
            font-weight: 600;
            color: #2c3e50;
        }

        .notification-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #7f8c8d;
        }

        .notification-message {
            color: #5a6c7d;
            font-size: 0.9rem;
        }

        .notification-time {
            font-size: 0.8rem;
            color: #95a5a6;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">
        <i class="fas fa-wifi"></i> Connecting...
    </div>

    <div class="notification-toast" id="notificationToast">
        <div class="notification-header">
            <div class="notification-title" id="notificationTitle"></div>
            <button class="notification-close" onclick="hideNotification()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="notification-message" id="notificationMessage"></div>
        <div class="notification-time" id="notificationTime"></div>
    </div>

    <div class="dashboard-container">
        <!-- System Overview Card -->
        <div class="dashboard-card">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-tachometer-alt card-icon"></i>
                    System Overview
                </div>
                <span class="status-indicator status-online"></span>
            </div>
            <div class="metric-value" id="totalUAVs">0</div>
            <div class="metric-label">Total UAVs</div>
            <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                <div>
                    <div class="metric-value" style="font-size: 1.5rem; color: #27ae60;" id="authorizedUAVs">0</div>
                    <div class="metric-label">Authorized</div>
                </div>
                <div>
                    <div class="metric-value" style="font-size: 1.5rem; color: #e74c3c;" id="unauthorizedUAVs">0</div>
                    <div class="metric-label">Unauthorized</div>
                </div>
            </div>
        </div>

        <!-- Flight Activity Card -->
        <div class="dashboard-card">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-plane card-icon"></i>
                    Flight Activity
                </div>
            </div>
            <div class="metric-value" id="activeFlights">0</div>
            <div class="metric-label">Active Flights</div>
            <div style="margin-top: 15px;">
                <div>Today's Flights: <strong id="todayFlights">0</strong></div>
                <div>Completed: <strong id="completedFlights">0</strong></div>
            </div>
        </div>

        <!-- Battery Status Card -->
        <div class="dashboard-card">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-battery-three-quarters card-icon"></i>
                    Battery Status
                </div>
            </div>
            <div style="display: flex; justify-content: space-between;">
                <div>
                    <div class="metric-value" style="font-size: 1.5rem; color: #e74c3c;" id="lowBatteryCount">0</div>
                    <div class="metric-label">Low Battery</div>
                </div>
                <div>
                    <div class="metric-value" style="font-size: 1.5rem; color: #f39c12;" id="chargingCount">0</div>
                    <div class="metric-label">Charging</div>
                </div>
            </div>
        </div>

        <!-- Hibernate Pod Card -->
        <div class="dashboard-card">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-bed card-icon"></i>
                    Hibernate Pod
                </div>
            </div>
            <div class="metric-value" id="hibernatePodCapacity">0/5</div>
            <div class="metric-label">Capacity</div>
            <div style="margin-top: 15px;">
                <div style="background: #f0f0f0; height: 20px; border-radius: 10px; overflow: hidden;">
                    <div id="hibernatePodBar" style="height: 100%; background: linear-gradient(90deg, #3498db, #2c3e50); width: 0%; transition: width 0.3s ease;"></div>
                </div>
                <div style="margin-top: 10px; font-size: 0.9rem; color: #7f8c8d;">
                    Utilization: <span id="hibernatePodUtilization">0%</span>
                </div>
            </div>
        </div>

        <!-- Real-time Alerts Card -->
        <div class="dashboard-card" style="grid-column: span 2;">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-exclamation-triangle card-icon"></i>
                    Real-time Alerts
                </div>
            </div>
            <div class="alert-list" id="alertList">
                <div class="alert-item alert-info">
                    <i class="fas fa-info-circle" style="margin-right: 10px; color: #3498db;"></i>
                    <div>System monitoring active. Waiting for alerts...</div>
                </div>
            </div>
        </div>

        <!-- UAV Status Grid -->
        <div class="dashboard-card" style="grid-column: span 2;">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-drone card-icon"></i>
                    UAV Status Grid
                </div>
            </div>
            <div class="uav-grid" id="uavGrid">
                <!-- UAV cards will be populated here -->
            </div>
        </div>

        <!-- Flight Activity Chart -->
        <div class="dashboard-card" style="grid-column: span 2;">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-chart-line card-icon"></i>
                    Flight Activity Trends
                </div>
            </div>
            <div class="chart-container">
                <canvas id="flightChart"></canvas>
            </div>
        </div>
    </div>

    <div class="last-updated">
        Last updated: <span id="lastUpdated">Never</span>
    </div>

    <script>
        let stompClient = null;
        let flightChart = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeWebSocket();
            initializeCharts();
            loadInitialData();
        });

        function initializeWebSocket() {
            updateConnectionStatus('connecting');
            
            const socket = new SockJS('/ws/dashboard');
            stompClient = Stomp.over(socket);
            
            stompClient.connect({}, function(frame) {
                console.log('Connected: ' + frame);
                updateConnectionStatus('connected');
                
                // Subscribe to system statistics
                stompClient.subscribe('/topic/system-stats', function(message) {
                    const stats = JSON.parse(message.body);
                    updateSystemStats(stats);
                });
                
                // Subscribe to UAV status updates
                stompClient.subscribe('/topic/uav-status', function(message) {
                    const uavStatus = JSON.parse(message.body);
                    updateUAVGrid(uavStatus);
                });
                
                // Subscribe to battery alerts
                stompClient.subscribe('/topic/battery-alerts', function(message) {
                    const alerts = JSON.parse(message.body);
                    handleBatteryAlerts(alerts);
                });
                
                // Subscribe to flight activity
                stompClient.subscribe('/topic/flight-activity', function(message) {
                    const activity = JSON.parse(message.body);
                    updateFlightActivity(activity);
                });
                
                // Subscribe to hibernate pod status
                stompClient.subscribe('/topic/hibernate-pod', function(message) {
                    const podStatus = JSON.parse(message.body);
                    updateHibernatePodStatus(podStatus);
                });
                
                // Subscribe to notifications
                stompClient.subscribe('/topic/notifications', function(message) {
                    const notification = JSON.parse(message.body);
                    showNotification(notification);
                });
                
                // Subscribe to emergency alerts
                stompClient.subscribe('/topic/emergency-alerts', function(message) {
                    const alert = JSON.parse(message.body);
                    handleEmergencyAlert(alert);
                });
                
            }, function(error) {
                console.log('Connection error: ' + error);
                updateConnectionStatus('disconnected');
                // Attempt to reconnect after 5 seconds
                setTimeout(initializeWebSocket, 5000);
            });
        }

        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = 'connection-status ' + status;
            
            switch(status) {
                case 'connected':
                    statusElement.innerHTML = '<i class="fas fa-wifi"></i> Connected';
                    break;
                case 'connecting':
                    statusElement.innerHTML = '<i class="fas fa-wifi pulse"></i> Connecting...';
                    break;
                case 'disconnected':
                    statusElement.innerHTML = '<i class="fas fa-wifi-slash"></i> Disconnected';
                    break;
            }
        }

        function updateSystemStats(stats) {
            if (stats.uav) {
                document.getElementById('totalUAVs').textContent = stats.uav.total || 0;
                document.getElementById('authorizedUAVs').textContent = stats.uav.authorized || 0;
                document.getElementById('unauthorizedUAVs').textContent = stats.uav.unauthorized || 0;
            }
            
            if (stats.flights) {
                document.getElementById('activeFlights').textContent = stats.flights.active || 0;
                document.getElementById('todayFlights').textContent = stats.flights.todayTotal || 0;
                document.getElementById('completedFlights').textContent = stats.flights.todayCompleted || 0;
            }
            
            if (stats.battery) {
                document.getElementById('lowBatteryCount').textContent = stats.battery.lowBattery || 0;
                document.getElementById('chargingCount').textContent = stats.battery.charging || 0;
            }
            
            document.getElementById('lastUpdated').textContent = new Date().toLocaleTimeString();
        }

        function updateUAVGrid(uavStatus) {
            const grid = document.getElementById('uavGrid');
            grid.innerHTML = '';
            
            Object.values(uavStatus).forEach(uav => {
                const uavCard = document.createElement('div');
                uavCard.className = 'uav-card';
                
                const batteryLevel = uav.batteryLevel || 0;
                const batteryClass = batteryLevel > 50 ? 'battery-high' : 
                                   batteryLevel > 20 ? 'battery-medium' : 'battery-low';
                
                uavCard.innerHTML = `
                    <div style="font-weight: 600; margin-bottom: 10px;">${uav.rfidTag}</div>
                    <div class="uav-status status-${uav.status.toLowerCase()}">${uav.status}</div>
                    <div class="battery-indicator">
                        <div class="battery-bar">
                            <div class="battery-fill ${batteryClass}" style="width: ${batteryLevel}%;"></div>
                        </div>
                        <span>${batteryLevel}%</span>
                    </div>
                    <div style="font-size: 0.8rem; color: #6c757d;">${uav.operationalStatus}</div>
                `;
                
                grid.appendChild(uavCard);
            });
        }

        function updateHibernatePodStatus(podStatus) {
            const capacity = `${podStatus.currentCapacity}/${podStatus.maxCapacity}`;
            const utilization = Math.round(podStatus.utilizationPercentage) + '%';
            
            document.getElementById('hibernatePodCapacity').textContent = capacity;
            document.getElementById('hibernatePodUtilization').textContent = utilization;
            document.getElementById('hibernatePodBar').style.width = podStatus.utilizationPercentage + '%';
        }

        function handleBatteryAlerts(alerts) {
            if (alerts.lowBattery > 0 || alerts.criticalBattery > 0 || alerts.overheating > 0) {
                addAlert('warning', `Battery Alert: ${alerts.lowBattery} low, ${alerts.criticalBattery} critical, ${alerts.overheating} overheating`);
            }
        }

        function updateFlightActivity(activity) {
            document.getElementById('activeFlights').textContent = activity.activeFlights || 0;
            
            if (activity.flights && activity.flights.length > 0) {
                activity.flights.forEach(flight => {
                    addAlert('info', `Flight in progress: ${flight.missionName} (${flight.uavRfid})`);
                });
            }
        }

        function addAlert(type, message) {
            const alertList = document.getElementById('alertList');
            const alertItem = document.createElement('div');
            alertItem.className = `alert-item alert-${type}`;
            
            const icon = type === 'warning' ? 'fa-exclamation-triangle' : 
                        type === 'error' ? 'fa-times-circle' : 'fa-info-circle';
            const color = type === 'warning' ? '#f39c12' : 
                         type === 'error' ? '#e74c3c' : '#3498db';
            
            alertItem.innerHTML = `
                <i class="fas ${icon}" style="margin-right: 10px; color: ${color};"></i>
                <div>
                    <div>${message}</div>
                    <div style="font-size: 0.8rem; color: #7f8c8d; margin-top: 5px;">
                        ${new Date().toLocaleTimeString()}
                    </div>
                </div>
            `;
            
            alertList.insertBefore(alertItem, alertList.firstChild);
            
            // Remove old alerts (keep only last 10)
            while (alertList.children.length > 10) {
                alertList.removeChild(alertList.lastChild);
            }
        }

        function showNotification(notification) {
            document.getElementById('notificationTitle').textContent = notification.title;
            document.getElementById('notificationMessage').textContent = notification.message;
            document.getElementById('notificationTime').textContent = new Date(notification.timestamp).toLocaleTimeString();
            
            const toast = document.getElementById('notificationToast');
            toast.classList.add('show');
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                hideNotification();
            }, 5000);
        }

        function hideNotification() {
            document.getElementById('notificationToast').classList.remove('show');
        }

        function handleEmergencyAlert(alert) {
            addAlert('error', `EMERGENCY: ${alert.alertType} - ${alert.description} (${alert.uavRfid})`);
            showNotification({
                title: 'EMERGENCY ALERT',
                message: `${alert.alertType}: ${alert.description}`,
                timestamp: alert.timestamp
            });
        }

        function initializeCharts() {
            const ctx = document.getElementById('flightChart').getContext('2d');
            flightChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Flights',
                        data: [],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function loadInitialData() {
            // Load initial analytics data
            fetch('/api/analytics/dashboard')
                .then(response => response.json())
                .then(data => {
                    updateSystemStats(data);
                })
                .catch(error => {
                    console.error('Error loading initial data:', error);
                    addAlert('error', 'Failed to load initial dashboard data');
                });
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (stompClient !== null) {
                stompClient.disconnect();
            }
        });
    </script>
</body>
</html>
