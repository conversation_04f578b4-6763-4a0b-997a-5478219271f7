# UAV Map Positioning API Documentation

This document provides comprehensive documentation for the UAV Map Positioning functionality, including location tracking, geofencing, and docking station management APIs.

## Table of Contents

1. [Authentication](#authentication)
2. [Location Tracking APIs](#location-tracking-apis)
3. [Docking Station APIs](#docking-station-apis)
4. [Geofence APIs](#geofence-apis)
5. [WebSocket Real-time Updates](#websocket-real-time-updates)
6. [<PERSON>rro<PERSON> Handling](#error-handling)
7. [Data Models](#data-models)

## Authentication

All API endpoints require authentication. Use the following roles:

- **USER**: Read-only access to location data, stations, and geofences
- **OPERATOR**: Can update locations, manage stations and geofences
- **ADMIN**: Full access including deletion operations

## Location Tracking APIs

### Update UAV Location

**POST** `/api/location/update/{uavId}`

Updates the current location of a UAV and creates a location history record.

**Required Role:** OPERATOR, ADMIN

**Path Parameters:**
- `uavId` (integer): The ID of the UAV

**Request Body:**
```json
{
  "latitude": 40.7128,
  "longitude": -74.0060,
  "altitude": 50.0,
  "speed": 25.5,
  "heading": 180.0,
  "batteryLevel": 85,
  "accuracy": 2.5,
  "signalStrength": 90,
  "source": "GPS"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Location updated successfully",
  "timestamp": "2024-01-15T10:30:00"
}
```

### Get Current UAV Locations

**GET** `/api/location/current`

Retrieves current locations of all UAVs with location data.

**Required Role:** USER, OPERATOR, ADMIN

**Response:**
```json
[
  {
    "uavId": 1,
    "rfidTag": "UAV-001",
    "ownerName": "John Smith",
    "model": "DJI Phantom 4",
    "status": "AUTHORIZED",
    "operationalStatus": "IN_FLIGHT",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "altitude": 50.0,
    "lastUpdate": "2024-01-15T10:30:00",
    "speed": 25.5,
    "heading": 180.0,
    "batteryLevel": 85,
    "accuracy": 2.5,
    "signalStrength": 90
  }
]
```

### Get UAV Location History

**GET** `/api/location/history/{uavId}`

Retrieves location history for a specific UAV.

**Required Role:** USER, OPERATOR, ADMIN

**Path Parameters:**
- `uavId` (integer): The ID of the UAV

**Query Parameters:**
- `limit` (integer, optional): Maximum number of records to return (default: 100)

**Response:**
```json
[
  {
    "id": 1,
    "timestamp": "2024-01-15T10:30:00",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "altitudeMeters": 50.0,
    "speedKmh": 25.5,
    "headingDegrees": 180.0,
    "batteryLevel": 85,
    "locationSource": "GPS",
    "accuracyMeters": 2.5,
    "signalStrength": 90
  }
]
```

### Get Flight Path

**GET** `/api/location/flight-path/{uavId}`

Retrieves the flight path for a UAV within a specified time range.

**Required Role:** USER, OPERATOR, ADMIN

**Path Parameters:**
- `uavId` (integer): The ID of the UAV

**Query Parameters:**
- `startTime` (ISO datetime): Start time for the flight path
- `endTime` (ISO datetime): End time for the flight path

**Example:** `/api/location/flight-path/1?startTime=2024-01-15T08:00:00&endTime=2024-01-15T12:00:00`

### Bulk Location Update

**POST** `/api/location/bulk-update`

Updates locations for multiple UAVs in a single request.

**Required Role:** OPERATOR, ADMIN

**Request Body:**
```json
[
  {
    "uavId": 1,
    "latitude": 40.7128,
    "longitude": -74.0060,
    "altitude": 50.0,
    "speed": 25.5,
    "batteryLevel": 85
  },
  {
    "uavId": 2,
    "latitude": 40.7589,
    "longitude": -73.9851,
    "altitude": 75.0,
    "speed": 30.0,
    "batteryLevel": 78
  }
]
```

## Docking Station APIs

### Create Docking Station

**POST** `/api/docking-stations`

Creates a new docking station.

**Required Role:** ADMIN

**Request Body:**
```json
{
  "name": "Central Hub",
  "description": "Main docking facility",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "altitudeMeters": 10.0,
  "maxCapacity": 10,
  "stationType": "CHARGING",
  "chargingAvailable": true,
  "maintenanceAvailable": true,
  "weatherProtected": true,
  "securityLevel": "HIGH",
  "contactInfo": "<EMAIL>"
}
```

### Get All Docking Stations

**GET** `/api/docking-stations`

Retrieves all docking stations.

**Required Role:** USER, OPERATOR, ADMIN

### Find Nearest Stations

**GET** `/api/docking-stations/nearest`

Finds the nearest docking stations to a given location.

**Required Role:** USER, OPERATOR, ADMIN

**Query Parameters:**
- `latitude` (double): Latitude coordinate
- `longitude` (double): Longitude coordinate
- `limit` (integer, optional): Maximum number of stations to return (default: 5)

### Get Available Stations

**GET** `/api/docking-stations/available`

Retrieves all available docking stations (operational and not full).

**Required Role:** USER, OPERATOR, ADMIN

## Geofence APIs

### Create Geofence

**POST** `/api/geofences`

Creates a new geofence.

**Required Role:** OPERATOR, ADMIN

**Request Body:**
```json
{
  "name": "Operational Zone",
  "description": "Main operational area",
  "fenceType": "CIRCULAR",
  "boundaryType": "INCLUSION",
  "centerLatitude": 40.7128,
  "centerLongitude": -74.0060,
  "radiusMeters": 5000.0,
  "priorityLevel": 2,
  "violationAction": "RETURN_TO_BASE",
  "maxAltitudeMeters": 120.0,
  "notificationEmails": "<EMAIL>"
}
```

### Create Circular Geofence

**POST** `/api/geofences/circular`

Creates a circular geofence with simplified parameters.

**Required Role:** OPERATOR, ADMIN

**Request Body:**
```json
{
  "name": "Test Zone",
  "centerLatitude": 40.7128,
  "centerLongitude": -74.0060,
  "radiusMeters": 1000.0,
  "boundaryType": "INCLUSION",
  "description": "Test circular geofence",
  "priorityLevel": 2,
  "violationAction": "ALERT"
}
```

### Check Point Against Geofences

**GET** `/api/geofences/check-point`

Checks if a point violates any active geofences.

**Required Role:** USER, OPERATOR, ADMIN

**Query Parameters:**
- `latitude` (double): Latitude coordinate
- `longitude` (double): Longitude coordinate
- `altitude` (double, optional): Altitude in meters

**Response:**
```json
{
  "success": true,
  "latitude": 40.7128,
  "longitude": -74.0060,
  "altitude": 50.0,
  "violations": [],
  "containments": [
    {
      "geofenceId": 1,
      "name": "Operational Zone",
      "type": "CIRCULAR",
      "boundaryType": "INCLUSION",
      "priorityLevel": 2,
      "isInside": true,
      "altitudeViolation": false
    }
  ],
  "hasViolations": false,
  "checkedGeofences": 3,
  "timestamp": "2024-01-15T10:30:00"
}
```

### Get Active Geofences

**GET** `/api/geofences/active`

Retrieves all currently active geofences.

**Required Role:** USER, OPERATOR, ADMIN

## WebSocket Real-time Updates

The system provides real-time updates via WebSocket connections at `/ws`.

### Connection

Connect to: `ws://localhost:8080/ws`

### Topics

#### Location Updates
- **Topic:** `/topic/location-updates`
- **Description:** Real-time UAV location updates

**Message Format:**
```json
{
  "type": "LOCATION_UPDATE",
  "timestamp": "2024-01-15T10:30:00",
  "uavId": 1,
  "rfidTag": "UAV-001",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "altitude": 50.0,
  "speed": 25.5,
  "heading": 180.0,
  "batteryLevel": 85,
  "status": "AUTHORIZED",
  "operationalStatus": "IN_FLIGHT"
}
```

#### Geofence Violations
- **Topic:** `/topic/geofence-violations`
- **Description:** Real-time geofence violation alerts

**Message Format:**
```json
{
  "type": "GEOFENCE_VIOLATION",
  "timestamp": "2024-01-15T10:30:00",
  "uavId": 1,
  "uavRfidTag": "UAV-001",
  "geofenceId": 1,
  "geofenceName": "Restricted Zone",
  "boundaryType": "EXCLUSION",
  "violationAction": "EMERGENCY_LAND",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "altitude": 50.0,
  "priorityLevel": 5
}
```

#### Docking Events
- **Topic:** `/topic/docking-events`
- **Description:** UAV docking and undocking events

#### System Alerts
- **Topic:** `/topic/alerts`
- **Description:** General system alerts and notifications

## Error Handling

### Standard Error Response

```json
{
  "success": false,
  "message": "Error description",
  "timestamp": "2024-01-15T10:30:00",
  "errorCode": "ERROR_CODE"
}
```

### Common HTTP Status Codes

- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource conflict (e.g., duplicate name)
- **500 Internal Server Error**: Server error

## Data Models

### LocationHistory

```json
{
  "id": 1,
  "timestamp": "2024-01-15T10:30:00",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "altitudeMeters": 50.0,
  "speedKmh": 25.5,
  "headingDegrees": 180.0,
  "batteryLevel": 85,
  "locationSource": "GPS",
  "accuracyMeters": 2.5,
  "signalStrength": 90,
  "weatherConditions": "Clear",
  "notes": "Normal flight"
}
```

### DockingStation

```json
{
  "id": 1,
  "name": "Central Hub",
  "description": "Main docking facility",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "altitudeMeters": 10.0,
  "maxCapacity": 10,
  "currentOccupancy": 3,
  "status": "OPERATIONAL",
  "stationType": "CHARGING",
  "chargingAvailable": true,
  "maintenanceAvailable": true,
  "weatherProtected": true,
  "securityLevel": "HIGH",
  "contactInfo": "<EMAIL>",
  "createdAt": "2024-01-15T08:00:00",
  "updatedAt": "2024-01-15T10:30:00"
}
```

### Geofence

```json
{
  "id": 1,
  "name": "Operational Zone",
  "description": "Main operational area",
  "fenceType": "CIRCULAR",
  "boundaryType": "INCLUSION",
  "status": "ACTIVE",
  "centerLatitude": 40.7128,
  "centerLongitude": -74.0060,
  "radiusMeters": 5000.0,
  "minAltitudeMeters": 10.0,
  "maxAltitudeMeters": 120.0,
  "priorityLevel": 2,
  "violationAction": "RETURN_TO_BASE",
  "notificationEmails": "<EMAIL>",
  "activeFrom": "2024-01-15T00:00:00",
  "activeUntil": "2024-12-31T23:59:59",
  "totalViolations": 5,
  "lastViolationTime": "2024-01-15T09:45:00",
  "createdAt": "2024-01-15T08:00:00",
  "updatedAt": "2024-01-15T10:30:00"
}
```

## Rate Limiting

- Location updates: 60 requests per minute per UAV
- Bulk updates: 10 requests per minute
- General API calls: 1000 requests per hour per user

## Security Considerations

1. All location data is encrypted in transit using HTTPS
2. WebSocket connections require authentication
3. Geofence violations trigger immediate alerts
4. Location history is automatically cleaned up after 90 days
5. API access is logged for audit purposes

## Examples

### JavaScript Client Example

```javascript
// Update UAV location
const updateLocation = async (uavId, locationData) => {
  const response = await fetch(`/api/location/update/${uavId}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify(locationData)
  });
  return response.json();
};

// WebSocket connection
const socket = new SockJS('/ws');
const stompClient = Stomp.over(socket);

stompClient.connect({}, () => {
  stompClient.subscribe('/topic/location-updates', (message) => {
    const locationUpdate = JSON.parse(message.body);
    updateMapMarker(locationUpdate);
  });
});
```

For more examples and detailed integration guides, see the [Integration Examples](INTEGRATION_EXAMPLES.md) document.
