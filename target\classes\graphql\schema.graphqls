# GraphQL Schema for UAV Docking Management System
# Provides flexible querying and real-time subscriptions

scalar DateTime
scalar JSON

# Root Query Type
type Query {
    # UAV Queries
    uavs(filter: UAVFilter, pagination: PaginationInput): UAVConnection!
    uav(id: ID!): UAV
    uavByRfid(rfidTag: String!): UAV
    uavStatistics: UAVStatistics!
    
    # Flight Log Queries
    flightLogs(filter: FlightLogFilter, pagination: PaginationInput): FlightLogConnection!
    flightLog(id: ID!): FlightLog
    flightStatistics(uavId: ID, dateRange: DateRangeInput): FlightStatistics!
    
    # Maintenance Queries
    maintenanceRecords(filter: MaintenanceFilter, pagination: PaginationInput): MaintenanceRecordConnection!
    maintenanceRecord(id: ID!): MaintenanceRecord
    upcomingMaintenance(days: Int = 7): [MaintenanceRecord!]!
    overdueMaintenance: [MaintenanceRecord!]!
    
    # Battery Queries
    batteryStatuses(filter: BatteryFilter): [BatteryStatus!]!
    batteryStatus(uavId: ID!): BatteryStatus
    lowBatteryUAVs(threshold: Int = 20): [BatteryStatus!]!
    batteryStatistics: BatteryStatistics!
    
    # Region Queries
    regions: [Region!]!
    region(id: ID!): Region
    
    # System Queries
    systemHealth: SystemHealth!
    dashboardData: DashboardData!
    hibernatePodStatus: HibernatePodStatus!
    
    # Analytics Queries
    flightTrends(period: TimePeriod!, dateRange: DateRangeInput): FlightTrends!
    performanceMetrics(uavId: ID): PerformanceMetrics!
    utilizationReport(dateRange: DateRangeInput): UtilizationReport!
}

# Root Mutation Type
type Mutation {
    # UAV Mutations
    createUAV(input: CreateUAVInput!): UAVMutationPayload!
    updateUAV(id: ID!, input: UpdateUAVInput!): UAVMutationPayload!
    deleteUAV(id: ID!): DeleteMutationPayload!
    updateUAVStatus(id: ID!, status: UAVStatus!): UAVMutationPayload!
    
    # Flight Log Mutations
    createFlightLog(input: CreateFlightLogInput!): FlightLogMutationPayload!
    startFlight(id: ID!): FlightLogMutationPayload!
    completeFlight(id: ID!, input: CompleteFlightInput!): FlightLogMutationPayload!
    abortFlight(id: ID!, reason: String!): FlightLogMutationPayload!
    recordEmergencyLanding(id: ID!, input: EmergencyLandingInput!): FlightLogMutationPayload!
    
    # Maintenance Mutations
    createMaintenanceRecord(input: CreateMaintenanceInput!): MaintenanceMutationPayload!
    updateMaintenanceRecord(id: ID!, input: UpdateMaintenanceInput!): MaintenanceMutationPayload!
    completeMaintenanceRecord(id: ID!, input: CompleteMaintenanceInput!): MaintenanceMutationPayload!
    
    # Hibernate Pod Mutations
    addToHibernatePod(uavId: ID!): HibernatePodMutationPayload!
    removeFromHibernatePod(uavId: ID!): HibernatePodMutationPayload!
    
    # Region Mutations
    createRegion(input: CreateRegionInput!): RegionMutationPayload!
    updateRegion(id: ID!, input: UpdateRegionInput!): RegionMutationPayload!
    deleteRegion(id: ID!): DeleteMutationPayload!
    assignUAVToRegion(uavId: ID!, regionId: ID!): UAVMutationPayload!
    removeUAVFromRegion(uavId: ID!, regionId: ID!): UAVMutationPayload!
}

# Root Subscription Type
type Subscription {
    # Real-time UAV updates
    uavStatusUpdated: UAV!
    uavAdded: UAV!
    uavRemoved: ID!
    
    # Real-time flight updates
    flightStarted: FlightLog!
    flightCompleted: FlightLog!
    flightAborted: FlightLog!
    emergencyLanding: FlightLog!
    
    # Real-time system updates
    systemStatsUpdated: SystemStats!
    batteryAlert: BatteryAlert!
    maintenanceAlert: MaintenanceAlert!
    hibernatePodUpdated: HibernatePodStatus!
    
    # Real-time notifications
    notificationReceived(userId: ID): Notification!
    emergencyAlert: EmergencyAlert!
}

# UAV Types
type UAV {
    id: ID!
    rfidTag: String!
    ownerName: String!
    model: String!
    status: UAVStatus!
    operationalStatus: OperationalStatus!
    inHibernatePod: Boolean!
    serialNumber: String
    manufacturer: String
    weightKg: Float
    maxFlightTimeMinutes: Int
    maxAltitudeMeters: Int
    maxSpeedKmh: Int
    totalFlightHours: Int!
    totalFlightCycles: Int!
    lastMaintenanceDate: DateTime
    nextMaintenanceDue: DateTime
    currentLocationLatitude: Float
    currentLocationLongitude: Float
    lastKnownLocationUpdate: DateTime
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relationships
    regions: [Region!]!
    flightLogs(limit: Int = 10): [FlightLog!]!
    maintenanceRecords(limit: Int = 10): [MaintenanceRecord!]!
    batteryStatus: BatteryStatus
    
    # Computed fields
    needsMaintenance: Boolean!
    isOperational: Boolean!
    hasLowBattery: Boolean!
    flightLogCount: Int!
    maintenanceRecordCount: Int!
}

type FlightLog {
    id: ID!
    uav: UAV!
    missionName: String!
    flightDurationMinutes: Int
    maxAltitudeMeters: Float
    distanceTraveledKm: Float
    startLatitude: Float
    startLongitude: Float
    endLatitude: Float
    endLongitude: Float
    batteryStartPercentage: Int
    batteryEndPercentage: Int
    weatherConditions: String
    flightStatus: FlightStatus!
    pilotName: String
    notes: String
    createdAt: DateTime!
    flightStartTime: DateTime
    flightEndTime: DateTime
    emergencyLanding: Boolean!
    payloadWeightKg: Float
    averageSpeedKmh: Float
    maxSpeedKmh: Float
    
    # Computed fields
    batteryConsumption: Int
    isFlightCompleted: Boolean!
    isEmergencyFlight: Boolean!
}

type MaintenanceRecord {
    id: ID!
    uav: UAV!
    maintenanceType: MaintenanceType!
    priority: Priority!
    status: MaintenanceStatus!
    title: String!
    description: String
    technicianName: String
    estimatedDurationHours: Int
    actualDurationHours: Int
    cost: Float
    partsReplaced: String
    scheduledDate: DateTime
    startedDate: DateTime
    completedDate: DateTime
    nextMaintenanceDate: DateTime
    flightHoursAtMaintenance: Int
    cyclesAtMaintenance: Int
    warrantyCovered: Boolean!
    externalService: Boolean!
    serviceProvider: String
    notes: String
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Computed fields
    isOverdue: Boolean!
    isCompleted: Boolean!
    isHighPriority: Boolean!
    durationVariance: Int
}

type BatteryStatus {
    id: ID!
    uav: UAV!
    currentChargePercentage: Int!
    voltage: Float
    currentAmperage: Float
    temperatureCelsius: Float
    capacityMah: Int!
    remainingCapacityMah: Int
    cycleCount: Int!
    healthPercentage: Int!
    chargingStatus: ChargingStatus!
    batteryCondition: BatteryCondition!
    estimatedFlightTimeMinutes: Int
    lastChargeDate: DateTime
    lastDischargeDate: DateTime
    chargingCyclesSinceMaintenance: Int!
    deepDischargeCount: Int!
    overchargeCount: Int!
    maxTemperatureRecorded: Float
    minVoltageRecorded: Float
    batterySerialNumber: String
    manufacturer: String
    model: String
    manufactureDate: DateTime
    warrantyExpiryDate: DateTime
    isCharging: Boolean!
    chargingStartTime: DateTime
    estimatedChargingTimeMinutes: Int
    lastUpdated: DateTime!
    
    # Computed fields
    needsMaintenance: Boolean!
    needsReplacement: Boolean!
    isLowBattery: Boolean!
    isCriticalBattery: Boolean!
    isOverheating: Boolean!
    chargingTimeElapsedMinutes: Int
}

type Region {
    id: ID!
    regionName: String!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relationships
    uavs: [UAV!]!
    
    # Computed fields
    uavCount: Int!
}

# Enums
enum UAVStatus {
    AUTHORIZED
    UNAUTHORIZED
}

enum OperationalStatus {
    READY
    IN_FLIGHT
    MAINTENANCE
    CHARGING
    HIBERNATING
    OUT_OF_SERVICE
    EMERGENCY
    LOST_COMMUNICATION
}

enum FlightStatus {
    PLANNED
    IN_PROGRESS
    COMPLETED
    ABORTED
    EMERGENCY_LANDED
    CANCELLED
}

enum MaintenanceType {
    ROUTINE_INSPECTION
    PREVENTIVE_MAINTENANCE
    CORRECTIVE_MAINTENANCE
    EMERGENCY_REPAIR
    BATTERY_SERVICE
    PROPELLER_REPLACEMENT
    MOTOR_SERVICE
    SENSOR_CALIBRATION
    SOFTWARE_UPDATE
    STRUCTURAL_REPAIR
    ANNUAL_INSPECTION
    PRE_FLIGHT_CHECK
    POST_FLIGHT_CHECK
}

enum Priority {
    LOW
    MEDIUM
    HIGH
    CRITICAL
    EMERGENCY
}

enum MaintenanceStatus {
    SCHEDULED
    IN_PROGRESS
    COMPLETED
    CANCELLED
    POSTPONED
    WAITING_PARTS
    WAITING_APPROVAL
}

enum ChargingStatus {
    DISCONNECTED
    CONNECTED_NOT_CHARGING
    CHARGING
    FULLY_CHARGED
    CHARGING_ERROR
    OVERHEATING
    MAINTENANCE_MODE
}

enum BatteryCondition {
    EXCELLENT
    GOOD
    FAIR
    POOR
    CRITICAL
    REPLACE_SOON
    REPLACE_NOW
}

enum TimePeriod {
    HOURLY
    DAILY
    WEEKLY
    MONTHLY
    YEARLY
}

# Input Types
input UAVFilter {
    status: UAVStatus
    operationalStatus: OperationalStatus
    inHibernatePod: Boolean
    manufacturer: String
    model: String
    regionId: ID
    needsMaintenance: Boolean
    hasLowBattery: Boolean
}

input FlightLogFilter {
    uavId: ID
    flightStatus: FlightStatus
    pilotName: String
    emergencyLanding: Boolean
    dateRange: DateRangeInput
    missionName: String
}

input MaintenanceFilter {
    uavId: ID
    maintenanceType: MaintenanceType
    status: MaintenanceStatus
    priority: Priority
    technicianName: String
    overdue: Boolean
    dateRange: DateRangeInput
}

input BatteryFilter {
    chargingStatus: ChargingStatus
    batteryCondition: BatteryCondition
    lowBattery: Boolean
    criticalBattery: Boolean
    needsMaintenance: Boolean
    needsReplacement: Boolean
}

input PaginationInput {
    first: Int = 10
    after: String
    last: Int
    before: String
}

input DateRangeInput {
    startDate: DateTime!
    endDate: DateTime!
}

input CreateUAVInput {
    rfidTag: String!
    ownerName: String!
    model: String!
    status: UAVStatus = AUTHORIZED
    serialNumber: String
    manufacturer: String
    weightKg: Float
    maxFlightTimeMinutes: Int
    maxAltitudeMeters: Int
    maxSpeedKmh: Int
    regionIds: [ID!]
    inHibernatePod: Boolean = false
}

input UpdateUAVInput {
    rfidTag: String
    ownerName: String
    model: String
    status: UAVStatus
    operationalStatus: OperationalStatus
    serialNumber: String
    manufacturer: String
    weightKg: Float
    maxFlightTimeMinutes: Int
    maxAltitudeMeters: Int
    maxSpeedKmh: Int
    currentLocationLatitude: Float
    currentLocationLongitude: Float
}

input CreateFlightLogInput {
    uavId: ID!
    missionName: String!
    pilotName: String
    startLatitude: Float
    startLongitude: Float
    batteryStartPercentage: Int
    weatherConditions: String
    payloadWeightKg: Float
    notes: String
}

input CompleteFlightInput {
    flightDurationMinutes: Int
    maxAltitudeMeters: Float
    distanceTraveledKm: Float
    endLatitude: Float
    endLongitude: Float
    batteryEndPercentage: Int
    averageSpeedKmh: Float
    maxSpeedKmh: Float
    notes: String
}

input EmergencyLandingInput {
    latitude: Float!
    longitude: Float!
    reason: String!
}

input CreateMaintenanceInput {
    uavId: ID!
    maintenanceType: MaintenanceType!
    priority: Priority = MEDIUM
    title: String!
    description: String
    technicianName: String
    estimatedDurationHours: Int
    scheduledDate: DateTime
    warrantyCovered: Boolean = false
    externalService: Boolean = false
    serviceProvider: String
}

input UpdateMaintenanceInput {
    priority: Priority
    title: String
    description: String
    technicianName: String
    estimatedDurationHours: Int
    scheduledDate: DateTime
    status: MaintenanceStatus
    notes: String
}

input CompleteMaintenanceInput {
    actualDurationHours: Int!
    cost: Float
    partsReplaced: String
    nextMaintenanceDate: DateTime
    notes: String
}

input CreateRegionInput {
    regionName: String!
}

input UpdateRegionInput {
    regionName: String!
}

# Connection Types (for pagination)
type UAVConnection {
    edges: [UAVEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
}

type UAVEdge {
    node: UAV!
    cursor: String!
}

type FlightLogConnection {
    edges: [FlightLogEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
}

type FlightLogEdge {
    node: FlightLog!
    cursor: String!
}

type MaintenanceRecordConnection {
    edges: [MaintenanceRecordEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
}

type MaintenanceRecordEdge {
    node: MaintenanceRecord!
    cursor: String!
}

type PageInfo {
    hasNextPage: Boolean!
    hasPreviousPage: Boolean!
    startCursor: String
    endCursor: String
}

# Statistics and Analytics Types
type UAVStatistics {
    totalUAVs: Int!
    authorizedUAVs: Int!
    unauthorizedUAVs: Int!
    operationalUAVs: Int!
    maintenanceUAVs: Int!
    hibernatingUAVs: Int!
    inFlightUAVs: Int!
    lowBatteryUAVs: Int!
}

type FlightStatistics {
    totalFlights: Int!
    completedFlights: Int!
    activeFlights: Int!
    emergencyLandings: Int!
    totalFlightTimeMinutes: Int!
    totalDistanceKm: Float!
    averageFlightDurationMinutes: Float!
    maxAltitudeMeters: Float!
}

type BatteryStatistics {
    totalBatteries: Int!
    lowBatteryCount: Int!
    criticalBatteryCount: Int!
    chargingCount: Int!
    problemBatteryCount: Int!
    averageChargePercentage: Float!
    averageHealthPercentage: Float!
    averageCycleCount: Float!
}

type SystemHealth {
    status: String!
    uptime: String!
    lastCheck: DateTime!
    databaseStatus: String!
    memoryUsage: Float!
    cpuUsage: Float!
}

type DashboardData {
    uavStatistics: UAVStatistics!
    flightStatistics: FlightStatistics!
    batteryStatistics: BatteryStatistics!
    hibernatePodStatus: HibernatePodStatus!
    recentAlerts: [Alert!]!
    systemHealth: SystemHealth!
}

type HibernatePodStatus {
    currentCapacity: Int!
    maxCapacity: Int!
    availableCapacity: Int!
    isFull: Boolean!
    utilizationPercentage: Float!
    uavs: [UAV!]!
}

type FlightTrends {
    period: TimePeriod!
    data: [TrendDataPoint!]!
}

type TrendDataPoint {
    timestamp: DateTime!
    value: Float!
    label: String!
}

type PerformanceMetrics {
    reliability: Float!
    efficiency: Float!
    utilizationRate: Float!
    maintenanceFrequency: Float!
    averageFlightDuration: Float!
    totalOperatingHours: Float!
}

type UtilizationReport {
    overallUtilization: Float!
    uavUtilization: [UAVUtilization!]!
    peakHours: [Int!]!
    dailyAverages: [Float!]!
}

type UAVUtilization {
    uav: UAV!
    utilizationPercentage: Float!
    flightHours: Float!
    flightCount: Int!
}

# Mutation Payload Types
type UAVMutationPayload {
    uav: UAV
    success: Boolean!
    message: String!
    errors: [String!]!
}

type FlightLogMutationPayload {
    flightLog: FlightLog
    success: Boolean!
    message: String!
    errors: [String!]!
}

type MaintenanceMutationPayload {
    maintenanceRecord: MaintenanceRecord
    success: Boolean!
    message: String!
    errors: [String!]!
}

type HibernatePodMutationPayload {
    hibernatePodStatus: HibernatePodStatus
    success: Boolean!
    message: String!
    errors: [String!]!
}

type RegionMutationPayload {
    region: Region
    success: Boolean!
    message: String!
    errors: [String!]!
}

type DeleteMutationPayload {
    success: Boolean!
    message: String!
    errors: [String!]!
}

# Subscription Types
type SystemStats {
    uavStatistics: UAVStatistics!
    flightStatistics: FlightStatistics!
    batteryStatistics: BatteryStatistics!
    timestamp: DateTime!
}

type BatteryAlert {
    uav: UAV!
    batteryLevel: Int!
    alertType: String!
    severity: String!
    timestamp: DateTime!
}

type MaintenanceAlert {
    maintenanceRecord: MaintenanceRecord!
    alertType: String!
    severity: String!
    timestamp: DateTime!
}

type Notification {
    id: ID!
    title: String!
    message: String!
    type: String!
    priority: String!
    timestamp: DateTime!
    actionUrl: String
    actionText: String
}

type EmergencyAlert {
    uav: UAV!
    alertType: String!
    description: String!
    latitude: Float
    longitude: Float
    severity: String!
    timestamp: DateTime!
}

type Alert {
    id: ID!
    type: String!
    severity: String!
    title: String!
    message: String!
    timestamp: DateTime!
    entityType: String
    entityId: String
}
