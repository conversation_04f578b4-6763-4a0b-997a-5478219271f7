/* Global Reset and Body Styling */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700&display=swap');

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --gradient-primary: linear-gradient(135deg, #3498db, #2c3e50);
    --gradient-accent: linear-gradient(135deg, #e74c3c, #f39c12);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.15);
    --border-radius: 8px;
    --transition-speed: 0.3s;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: #f0f5ff;
    color: var(--dark-color);
    padding: 20px;
    background-image: 
        radial-gradient(circle at 10% 20%, rgba(216, 241, 230, 0.4) 0%, rgba(233, 226, 226, 0) 40%),
        radial-gradient(circle at 90% 80%, rgba(211, 234, 255, 0.5) 0%, rgba(226, 226, 233, 0) 40%);
    background-attachment: fixed;
    min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Orbitron', sans-serif;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

h1 {
    font-size: 2.5rem;
    text-align: center;
    margin: 1.5rem 0;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    position: relative;
    padding-bottom: 10px;
}

h1::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

h2 {
    font-size: 1.8rem;
    color: var(--secondary-color);
    position: relative;
    padding-left: 15px;
    margin-top: 2rem;
}

h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 5px;
    background: var(--secondary-color);
    border-radius: 2px;
}

/* Container and Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    backdrop-filter: blur(5px);
}

/* Status Dashboard */
.status-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.status-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(52, 152, 219, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all var(--transition-speed);
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.status-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.status-icon.authorized {
    background: linear-gradient(135deg, var(--success-color), #27ae60);
}

.status-info h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    color: var(--primary-color);
    font-weight: 600;
}

.status-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin: 0;
    line-height: 1;
}

.status-label {
    font-size: 0.85rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.status-label.full {
    color: var(--danger-color);
    font-weight: 600;
}

.status-label.available {
    color: var(--success-color);
    font-weight: 600;
}

/* Form Styling */
form {
    background-color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    margin-bottom: 2rem;
    border: 1px solid rgba(52, 152, 219, 0.2);
    position: relative;
    overflow: hidden;
}

form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background: var(--gradient-primary);
}

.form-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: flex-start;
}

.form-group {
    margin-bottom: 1rem;
    position: relative;
    flex: 1 1 200px;
}

.form-inline {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-inline .form-group {
    flex: 1;
    min-width: 150px;
    margin-bottom: 0;
}

.form-group.full-width {
    flex: 1 1 100%;
    width: 100%;
}

.form-compact {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1.5rem;
    align-items: center;
}

.form-compact label {
    margin-bottom: 0;
    margin-right: 0.5rem;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 1rem;
    gap: 1rem;
    align-items: flex-end;
}

.form-col {
    flex: 1;
    min-width: 200px;
    position: relative;
    margin-bottom: 15px;
}

.form-icon {
    position: absolute;
    left: 12px;
    top: 38px;
    color: var(--secondary-color);
    font-size: 1.2rem;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.form-icon .fas,
.form-icon .fa,
.form-icon .far,
.form-icon .fab {
    display: inline-block;
    line-height: 1;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f8ff;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.form-icon:hover {
    transform: scale(1.1);
    background-color: #e3f2fd;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
    transition: all var(--transition-speed);
}

input[type="text"],
select {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all var(--transition-speed);
    background-color: #f9f9f9;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

input[type="text"]:focus,
select:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
    background-color: #fff;
}

input[type="text"]:focus + label,
select:focus + label {
    color: var(--secondary-color);
}

select[multiple] {
    height: auto;
    min-height: 120px;
}

.form-footer {
    grid-column: 1 / -1;
    display: flex;
    justify-content: flex-end;
    margin-top: 1rem;
    border-top: 1px solid #eee;
    padding-top: 1.5rem;
}

button {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-speed);
    box-shadow: var(--shadow-sm);
    text-transform: uppercase;
    letter-spacing: 1px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    opacity: 0.95;
}

button:active {
    transform: translateY(0);
}

.form-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-speed);
    border: 1px solid transparent;
    margin-bottom: 2rem;
    overflow: hidden;
}

.form-card-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.2rem;
    font-weight: 600;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-card-body {
    padding: 1.5rem;
}

/* Compact card styles for horizontal form */
.form-card.compact-form {
    background: linear-gradient(to right, rgba(52, 152, 219, 0.05), rgba(44, 62, 80, 0.05));
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.form-card.compact-form .form-card-body {
    padding: 1rem;
}

.form-card.compact-form form {
    border: none;
    padding: 0;
    margin: 0;
    box-shadow: none;
    background: transparent;
}

.form-card.compact-form label {
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: 500;
}

.form-card.compact-form input,
.form-card.compact-form select {
    border-radius: 4px;
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.form-card.compact-form button {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-card.compact-form .help-text {
    display: none;
}

@media (max-width: 1200px) {
    .form-row {
        flex-direction: column;
    }
    
    .form-col {
        width: 100%;
    }
    
    .form-card.compact-form button {
        margin-top: 1rem;
    }
}

.input-animated {
    position: relative;
    margin-bottom: 1.5rem;
}

.input-animated input,
.input-animated select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all var(--transition-speed);
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 1;
    position: relative;
}

.input-animated label {
    position: absolute;
    left: 15px;
    top: 12px;
    font-size: 1rem;
    color: #777;
    transition: all 0.2s ease;
    pointer-events: none;
    z-index: 2;
}

.input-animated input:focus ~ label,
.input-animated input:not(:placeholder-shown) ~ label,
.input-animated select:focus ~ label {
    top: -10px;
    left: 10px;
    font-size: 0.8rem;
    color: var(--secondary-color);
    background-color: white;
    padding: 0 5px;
}

.input-animated input:focus,
.input-animated select:focus {
    border: 2px solid var(--secondary-color);
    background-color: white;
}

.hover-card {
    transition: transform 0.2s, box-shadow 0.2s;
}

.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* Form section heading */
.form-section-heading {
    margin-bottom: 1.5rem;
    position: relative;
    display: flex;
    align-items: center;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #eee;
}

.form-section-heading i {
    font-size: 1.5rem;
    margin-right: 10px;
    color: var(--secondary-color);
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.form-section-heading h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.help-text {
    font-size: 0.85rem;
    color: #666;
    margin-top: 0.3rem;
}

/* Small indicator for required fields */
.required::after {
    content: '*';
    color: var(--danger-color);
    margin-left: 4px;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-footer {
        justify-content: center;
    }
}

/* Table Styling */
.table-container {
    overflow-x: auto;
    margin-bottom: 2rem;
}

.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-box input {
    width: 100%;
    padding: 10px 15px 10px 40px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    transition: all var(--transition-speed);
}

.search-box input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
}

.table-info {
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Bright header row class - apply this in combination with inline styles */
.bright-header {
    background-color: #2c3e50 !important;
    color: white !important;
    border-top: 3px solid #f39c12 !important;
    font-weight: bold !important;
}

.bright-header th {
    background-color: #2c3e50 !important;
    color: white !important;
    text-transform: uppercase !important;
    font-size: 1.1rem !important;
    border-bottom: 2px solid #3498db !important;
}

table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    background-color: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

/* Improve header row appearance */
thead tr {
    height: 60px; /* Taller header row for better visibility */
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

/* Add subtle top highlight */
thead::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: #3498db;
    z-index: 1;
}

/* Add subtle hover effect on header */
th:hover {
    background-color: #1a5278;
    transition: background-color 0.2s ease;
}

thead {
    background: #2c3e50 !important; /* Darker background with !important to force application */
    color: white !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.4) !important;
    position: relative;
    border-top: 3px solid #f39c12 !important; /* Orange top border for better visibility */
}

th {
    text-align: left;
    padding: 16px 15px;
    font-weight: 700;
    position: relative;
    letter-spacing: 0.7px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5) !important;
    font-size: 1.1rem !important;
    color: #ffffff !important;
    font-family: 'Poppins', sans-serif;
    text-transform: uppercase !important; /* Make column names uppercase for better visibility */
    /*  background-color: #2c3e50 !important; Ensure each cell has the background color */
}

th i {
    margin-right: 8px;
    font-size: 0.9rem;
    opacity: 0.9;
}

th:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 20%;
    height: 60%;
    width: 1px;
    background-color: rgba(255, 255, 255, 0.6);
}

/* Enhanced header with gradient and better contrast */
thead tr {
    background: linear-gradient(to bottom, #34495e, #2c3e50) !important;
    height: 65px; /* Slightly taller header for better visibility */
}

/* Add slight glow to header text for better readability */
th {
    position: relative;
    z-index: 1;
}

th::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: rgba(52, 152, 219, 0.5);
    transform: scaleX(0);
    transition: transform 0.3s ease;
    z-index: -1;
}

th:hover::before {
    transform: scaleX(1);
}

td {
    padding: 15px;
    border-bottom: 1px solid #eee;
    transition: background var(--transition-speed);
}

tr:last-child td {
    border-bottom: none;
}

tr:hover td {
    background-color: rgba(52, 152, 219, 0.05);
}

.regions {
    max-width: 250px;
    word-wrap: break-word;
}

/* Status Styling */
.status-pill {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}



.authorized {
    background-color: rgba(46, 204, 113, 0.15);
    color: var(--success-color);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.unauthorized {
    background-color: rgba(231, 76, 60, 0.15);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* Action Buttons */
.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: 0.85rem;
    text-decoration: none;
    transition: all var(--transition-speed);
    margin-right: 8px;
    box-shadow: var(--shadow-sm);
}

.btn-toggle {
    background-color: #3498db;
    color: white;
    border: 1px solid #2980b9;
}

.btn-toggle:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
}

.btn-delete {
    background-color: #e74c3c;
    color: white;
    border: 1px solid #c0392b;
}

.btn-delete:hover {
    background-color: #c0392b;
    transform: translateY(-2px);
}

.btn-add {
    background-color: #2ecc71;
    color: white;
    border: 1px solid #27ae60;
    padding: 4px 8px;
    font-size: 0.75rem;
    margin-left: 5px;
}

.btn-add:hover {
    background-color: #27ae60;
    transform: translateY(-1px);
}

.btn-remove {
    background-color: #e74c3c;
    color: white;
    border: 1px solid #c0392b;
    padding: 2px 6px;
    font-size: 0.7rem;
    margin-left: 2px;
    border-radius: 4px;
}

.btn-remove:hover {
    background-color: #c0392b;
}

/* Hibernate Pod Buttons */
.btn-hibernate-add {
    background-color: #9b59b6;
    color: white;
    border: 1px solid #8e44ad;
}

.btn-hibernate-add:hover {
    background-color: #8e44ad;
    transform: translateY(-2px);
}

.btn-hibernate-remove {
    background-color: #f39c12;
    color: white;
    border: 1px solid #e67e22;
}

.btn-hibernate-remove:hover {
    background-color: #e67e22;
    transform: translateY(-2px);
}

.btn-hibernate-add:disabled,
.btn-hibernate-remove:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Debug Box */
.debug-box {
    border: 1px solid rgba(231, 76, 60, 0.4);
    padding: 20px;
    margin-bottom: 30px;
    background-color: rgba(231, 76, 60, 0.05);
    border-radius: var(--border-radius);
    position: relative;
}

.debug-box h3 {
    color: var(--danger-color);
    margin-top: 0;
}

.debug-box::before {
    content: 'Debug Info';
    position: absolute;
    top: -12px;
    left: 20px;
    background-color: white;
    padding: 0 10px;
    font-size: 0.8rem;
    color: var(--danger-color);
    font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    form {
        padding: 1.5rem;
    }
    
    th, td {
        padding: 10px;
    }
    
    .action-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
    }
}

/* Animations */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Enhanced Error and Success Messages */
.message {
    padding: 12px 16px;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    animation: slideInDown 0.3s ease-out;
}

.message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.message.warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--secondary-color);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Validation Styles */
.field-error {
    color: var(--danger-color);
    font-size: 0.85rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 4px;
}

.field-error::before {
    content: '⚠';
    font-size: 0.9rem;
}

input.error,
select.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.25);
    background-color: rgba(231, 76, 60, 0.05);
}

input.success,
select.success {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.25);
    background-color: rgba(46, 204, 113, 0.05);
}

/* Field Warning Styles */
.field-warning {
    color: var(--warning-color);
    font-size: 0.85rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: rgba(243, 156, 18, 0.1);
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

/* Form Progress Indicator */
.form-progress {
    margin-top: 1rem;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background-color: #e0e0e0;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.3s ease;
    animation: progressAnimation 2s infinite;
}

@keyframes progressAnimation {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.progress-text {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin: 0;
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus indicators for better accessibility */
.focused,
button:focus,
a:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000000;
        --secondary-color: #0066cc;
        --light-color: #ffffff;
        --dark-color: #000000;
    }

    .status-pill {
        border-width: 2px;
        font-weight: bold;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .pulse {
        animation: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #e1e5e9;
        --secondary-color: #4a9eff;
        --light-color: #2c3e50;
        --dark-color: #e1e5e9;
    }

    body {
        background: #1a1a1a;
        color: #e1e5e9;
    }

    .container {
        background-color: rgba(44, 62, 80, 0.95);
    }

    .form-card,
    .status-card,
    table {
        background-color: #2c3e50;
        color: #e1e5e9;
    }

    input,
    select {
        background-color: #34495e;
        color: #e1e5e9;
        border-color: #4a5568;
    }
}

/* Footer Styles */
footer {
    margin-top: 3rem;
    padding: 2rem 0 1rem;
    border-top: 1px solid #eee;
    text-align: center;
    color: #666;
}

.accessibility-info {
    margin-top: 1rem;
    opacity: 0.8;
}

.accessibility-info small {
    font-size: 0.8rem;
    line-height: 1.4;
}

/* Table Pagination */
.table-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
    padding: 1rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.table-pagination button {
    padding: 8px 16px;
    border: 1px solid var(--secondary-color);
    background: white;
    color: var(--secondary-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-speed);
    font-size: 0.9rem;
}

.table-pagination button:hover:not(:disabled) {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-1px);
}

.table-pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.table-pagination span {
    font-weight: 500;
    color: var(--primary-color);
    min-width: 120px;
    text-align: center;
}

/* Performance optimizations */
.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy.loaded {
    opacity: 1;
}

/* Reduce animations for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
    .table-pagination button {
        transition: none;
    }

    .lazy {
        transition: none;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--secondary-color);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(3px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 500px;
    box-shadow: var(--shadow-lg);
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.modal-title {
    margin: 0;
    color: var(--primary-color);
}

.close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: auto;
    height: auto;
}

.close:hover {
    color: var(--danger-color);
    transform: none;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.modal-footer .action-btn {
    margin-right: 0;
}

/* Enhanced Button Styles */
.btn-secondary {
    background: #6c757d;
    color: white;
    border: 1px solid #5a6268;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    border: 1px solid #c0392b;
}

.btn-danger:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

/* Table Sorting Indicators */
th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

th.sortable:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

th.sortable::after {
    content: '↕';
    position: absolute;
    right: 8px;
    opacity: 0.5;
    font-size: 0.8rem;
}

th.sort-asc::after {
    content: '↑';
    opacity: 1;
}

th.sort-desc::after {
    content: '↓';
    opacity: 1;
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .container {
        margin: 0 1rem;
        padding: 1.5rem;
    }

    .form-row {
        flex-direction: column;
    }

    .form-col {
        width: 100%;
        min-width: auto;
    }
}

@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .container {
        padding: 1rem;
        margin: 0 0.5rem;
    }

    .form-card-body {
        padding: 1rem;
    }

    th, td {
        padding: 8px;
        font-size: 0.9rem;
    }

    .action-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
        margin-right: 4px;
    }

    .regions {
        max-width: 200px;
    }

    .modal-content {
        margin: 10% auto;
        width: 95%;
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .form-row {
        gap: 0.5rem;
    }

    .action-btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
        margin-right: 0;
    }

    .table-container {
        overflow-x: scroll;
    }

    table {
        min-width: 600px;
    }
}
