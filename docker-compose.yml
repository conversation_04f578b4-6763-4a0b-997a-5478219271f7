services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    depends_on:
      - db
    environment:
      SPRING_DATASOURCE_URL: jdbc:mysql://${DB_HOST}:${DB_PORT}/${DB_NAME}?useSSL=true&requireSSL=false&sslMode=REQUIRED&allowPublicKeyRetrieval=true&serverTimezone=UTC
      SPRING_DATASOURCE_USERNAME: ${DB_USER}
      SPRING_DATASOURCE_PASSWORD: ${DB_PASSWORD}

    networks:
      - uav-network

  db:
    image: mysql:8.0
    restart: always
    environment:
      MYSQL_DATABASE: uav_management_system
      MYSQL_ROOT_PASSWORD: younes123
    ports:
      - "3307:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - uav-network

networks:
  uav-network:
    driver: bridge

volumes:
  mysql-data:
