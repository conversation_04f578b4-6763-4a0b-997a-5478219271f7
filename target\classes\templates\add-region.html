<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Region to UAV - UAV Docking Management System</title>
    <link rel="stylesheet" type="text/css" href="/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <script src="/js/app.js" defer></script>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-plus-circle"></i> Add Region to UAV</h1>

        <div th:if="${availableRegions == null || #lists.isEmpty(availableRegions)}" class="message error">
            <i class="fas fa-exclamation-circle"></i>
            <div>
                <strong>No Available Regions</strong>
                <p>No available regions to add to this UAV. Either all regions are already assigned or no regions exist in the system.</p>
            </div>
        </div>

        <div th:unless="${availableRegions == null || #lists.isEmpty(availableRegions)}" class="form-card hover-card">
            <div class="form-card-header">
                <i class="fas fa-map-marker-alt"></i> Select Region to Add
            </div>
            <div class="form-card-body">
                <form th:action="@{/uav/{uavId}/add-region(uavId=${uavId})}" method="POST">
                    <div class="form-row">
                        <div class="form-col">
                            <label for="regionId" class="required">Available Regions</label>
                            <div class="form-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <select id="regionId" name="regionId" required>
                                <option value="">-- Select Region --</option>
                                <option th:each="region : ${availableRegions}"
                                        th:value="${region.id}"
                                        th:text="${region.regionName}"></option>
                            </select>
                        </div>
                        <div class="form-col" style="flex: 0 0 auto; display: flex; align-items: flex-end;">
                            <button type="submit">
                                <i class="fas fa-plus"></i> Add Region
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="form-footer">
            <a href="/" class="action-btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to UAV List
            </a>
        </div>
    </div>
</body>
</html>
