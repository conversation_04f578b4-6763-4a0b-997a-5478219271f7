# Advanced Configuration for UAV Docking Management System
# This configuration includes all advanced features and optimizations

spring:
  application:
    name: uav-docking-management-system
    version: 1.0.0
    
  profiles:
    active: development
    
  # Database Configuration
  datasource:
    url: jdbc:h2:mem:uavdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driverClassName: org.h2.Driver
    username: sa
    password: password
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1200000
      connection-timeout: 20000
      leak-detection-threshold: 60000
      
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
          fetch_size: 50
        cache:
          use_second_level_cache: true
          use_query_cache: true
          region:
            factory_class: org.hibernate.cache.jcache.JCacheRegionFactory
            
  # Cache Configuration
  cache:
    type: simple
    cache-names:
      - uavs
      - regions
      - statistics
      - hibernatePod
      - flightLogs
      - maintenanceRecords
      - batteryStatus
      - analytics
      - dashboardData
      
  # Security Configuration
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ${GOOGLE_CLIENT_ID:your-google-client-id}
            client-secret: ${GOOGLE_CLIENT_SECRET:your-google-client-secret}
            scope: openid,profile,email
          github:
            client-id: ${GITHUB_CLIENT_ID:your-github-client-id}
            client-secret: ${GITHUB_CLIENT_SECRET:your-github-client-secret}
            scope: user:email,read:user
            
  # Mail Configuration
  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-app-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          connectiontimeout: 5000
          timeout: 3000
          writetimeout: 5000
          
  # WebSocket Configuration
  websocket:
    allowed-origins: "*"
    sockjs:
      enabled: true
      heartbeat-time: 25000
      disconnect-delay: 5000
      
  # Actuator Configuration
  management:
    endpoints:
      web:
        exposure:
          include: health,info,metrics,prometheus,env,configprops,beans,mappings
        base-path: /actuator
    endpoint:
      health:
        show-details: when-authorized
        show-components: always
    health:
      diskspace:
        enabled: true
        threshold: 10GB
      db:
        enabled: true
    metrics:
      export:
        prometheus:
          enabled: true
      distribution:
        percentiles-histogram:
          http.server.requests: true
        percentiles:
          http.server.requests: 0.5,0.9,0.95,0.99
          
  # Task Execution Configuration
  task:
    execution:
      pool:
        core-size: 8
        max-size: 16
        queue-capacity: 100
        keep-alive: 60s
      thread-name-prefix: uav-task-
      
  # Scheduling Configuration
  task:
    scheduling:
      pool:
        size: 5
      thread-name-prefix: uav-scheduler-
      
  # Jackson Configuration
  jackson:
    serialization:
      write-dates-as-timestamps: false
      indent-output: true
    deserialization:
      fail-on-unknown-properties: false
    time-zone: UTC
    date-format: yyyy-MM-dd'T'HH:mm:ss.SSSZ
    
  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      file-size-threshold: 2KB
      
  # Session Configuration
  session:
    store-type: none
    timeout: 30m
    
# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /
    session:
      timeout: 30m
      cookie:
        http-only: true
        secure: false
        same-site: lax
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
  http2:
    enabled: true
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: on_param
    include-exception: false
    
# Logging Configuration
logging:
  level:
    com.example.uavdockingmanagementsystem: DEBUG
    org.springframework.security: INFO
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %highlight(%-5level) %cyan(%logger{36}) - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/uav-management-system.log
    max-size: 100MB
    max-history: 30
    total-size-cap: 10GB
    
# Application Specific Configuration
app:
  name: UAV Docking Management System
  version: 1.0.0
  description: Advanced UAV management system with real-time monitoring
  url: http://localhost:8080
  
  # Feature Flags
  features:
    real-time-monitoring: true
    email-notifications: true
    audit-logging: true
    rate-limiting: true
    websocket-support: true
    analytics-dashboard: true
    export-functionality: true
    api-documentation: true
    health-checks: true
    metrics-collection: true
    
  # Rate Limiting Configuration
  rate-limit:
    enabled: true
    default-limit: 100
    default-window: 60
    admin-limit: 1000
    api-key-limit: 2000
    
  # Notification Configuration
  notifications:
    email:
      enabled: true
      from: <EMAIL>
      templates-path: classpath:/templates/email/
    websocket:
      enabled: true
      broadcast-interval: 30
      
  # Security Configuration
  security:
    jwt:
      secret: ${JWT_SECRET:your-jwt-secret-key-here-make-it-long-and-secure}
      expiration: 86400 # 24 hours
      refresh-expiration: 604800 # 7 days
    api-keys:
      enabled: true
      default-expiry-days: 365
      max-keys-per-user: 5
    cors:
      allowed-origins: "*"
      allowed-methods: GET,POST,PUT,DELETE,PATCH,OPTIONS
      allowed-headers: "*"
      allow-credentials: true
      max-age: 3600
      
  # Monitoring Configuration
  monitoring:
    health-check-interval: 30
    metrics-collection-interval: 60
    alert-thresholds:
      memory-usage: 85
      cpu-usage: 80
      disk-usage: 90
      response-time: 5000
      
  # Cache Configuration
  cache:
    default-ttl: 300 # 5 minutes
    max-entries: 10000
    statistics-enabled: true
    
  # File Storage Configuration
  storage:
    upload-dir: ./uploads
    max-file-size: 10MB
    allowed-types: jpg,jpeg,png,pdf,doc,docx,xls,xlsx,csv
    
  # Export Configuration
  export:
    max-records: 10000
    formats: csv,excel,pdf,json
    async-threshold: 1000
    
  # Backup Configuration
  backup:
    enabled: true
    schedule: "0 0 2 * * ?" # Daily at 2 AM
    retention-days: 30
    location: ./backups
    
  # Integration Configuration
  integrations:
    external-apis:
      weather:
        enabled: false
        api-key: ${WEATHER_API_KEY:}
        base-url: https://api.openweathermap.org/data/2.5
      maps:
        enabled: false
        api-key: ${MAPS_API_KEY:}
        base-url: https://maps.googleapis.com/maps/api
        
# Development Profile Overrides
---
spring:
  config:
    activate:
      on-profile: development
      
  h2:
    console:
      enabled: true
      path: /h2-console
      
logging:
  level:
    com.example.uavdockingmanagementsystem: DEBUG
    org.springframework.web: DEBUG
    
server:
  port: 8080
  
app:
  security:
    cors:
      allowed-origins: "http://localhost:3000,http://localhost:8080"
      
# Production Profile Overrides
---
spring:
  config:
    activate:
      on-profile: production
      
  datasource:
    url: ${DATABASE_URL:**************************************}
    username: ${DATABASE_USERNAME:uav_user}
    password: ${DATABASE_PASSWORD:secure_password}
    driver-class-name: org.postgresql.Driver
    
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    
logging:
  level:
    com.example.uavdockingmanagementsystem: INFO
    org.springframework: WARN
    org.hibernate.SQL: WARN
    
server:
  port: ${PORT:8080}
  servlet:
    session:
      cookie:
        secure: true
        
app:
  security:
    cors:
      allowed-origins: ${ALLOWED_ORIGINS:https://yourdomain.com}
  notifications:
    email:
      from: ${EMAIL_FROM:<EMAIL>}
      
# Test Profile Overrides
---
spring:
  config:
    activate:
      on-profile: test
      
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    
logging:
  level:
    com.example.uavdockingmanagementsystem: DEBUG
    org.springframework: WARN
    
app:
  features:
    email-notifications: false
    real-time-monitoring: false
