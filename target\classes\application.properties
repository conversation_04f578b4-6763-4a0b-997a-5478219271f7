spring.application.name=UAV-Docking-Management-System
spring.datasource.url=jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:uav_management_system_new}?useSSL=true&requireSSL=false&sslMode=REQUIRED&allowPublicKeyRetrieval=true&serverTimezone=UTC
spring.datasource.username=${DB_USER:root}
spring.datasource.password=${DB_PASSWORD:younes123}
spring.jpa.hibernate.ddl-auto=update
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect



# Connection pool settings
spring.datasource.hikari.connection-timeout=120000
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.initialization-fail-timeout=120000

# JPA Settings
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.show-sql=true

# Show SQL statements for debugging
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.springframework.web=DEBUG
logging.level.com.zaxxer.hikari=DEBUG
logging.level.com.mysql.cj=DEBUG
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG

# Static resources configuration
spring.mvc.static-path-pattern=/resources/**
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.add-mappings=true

# Honour the PORT env var that Railway injects
server.port=${PORT:8080}