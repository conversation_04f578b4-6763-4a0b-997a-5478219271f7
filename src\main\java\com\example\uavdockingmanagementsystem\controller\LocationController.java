package com.example.uavdockingmanagementsystem.controller;

import com.example.uavdockingmanagementsystem.model.LocationHistory;
import com.example.uavdockingmanagementsystem.model.UAV;
import com.example.uavdockingmanagementsystem.repository.LocationHistoryRepository;
import com.example.uavdockingmanagementsystem.repository.UAVRepository;
import com.example.uavdockingmanagementsystem.service.LocationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST Controller for UAV location tracking and management
 * Provides endpoints for location updates, history, and real-time tracking
 */
@RestController
@RequestMapping("/api/location")
@CrossOrigin(origins = "*")
public class LocationController {

    @Autowired
    private LocationHistoryRepository locationHistoryRepository;

    @Autowired
    private UAVRepository uavRepository;

    @Autowired
    private LocationService locationService;

    /**
     * Update UAV location
     */
    @PostMapping("/update/{uavId}")
    public ResponseEntity<Map<String, Object>> updateLocation(
            @PathVariable Integer uavId,
            @RequestBody Map<String, Object> locationData) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Optional<UAV> uavOpt = uavRepository.findById(uavId);
            if (uavOpt.isEmpty()) {
                response.put("success", false);
                response.put("message", "UAV not found");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            UAV uav = uavOpt.get();
            
            // Extract location data
            Double latitude = Double.valueOf(locationData.get("latitude").toString());
            Double longitude = Double.valueOf(locationData.get("longitude").toString());
            Double altitude = locationData.containsKey("altitude") ? 
                Double.valueOf(locationData.get("altitude").toString()) : null;
            Double speed = locationData.containsKey("speed") ? 
                Double.valueOf(locationData.get("speed").toString()) : null;
            Double heading = locationData.containsKey("heading") ? 
                Double.valueOf(locationData.get("heading").toString()) : null;
            Integer batteryLevel = locationData.containsKey("batteryLevel") ? 
                Integer.valueOf(locationData.get("batteryLevel").toString()) : null;

            // Update UAV current location
            uav.setCurrentLatitude(latitude);
            uav.setCurrentLongitude(longitude);
            uav.setCurrentAltitudeMeters(altitude);
            uav.setLastLocationUpdate(LocalDateTime.now());
            uavRepository.save(uav);

            // Create location history record
            LocationHistory locationHistory = new LocationHistory(uav, latitude, longitude, altitude);
            locationHistory.setSpeedKmh(speed);
            locationHistory.setHeadingDegrees(heading);
            locationHistory.setBatteryLevel(batteryLevel);
            
            if (locationData.containsKey("accuracy")) {
                locationHistory.setAccuracyMeters(Double.valueOf(locationData.get("accuracy").toString()));
            }
            if (locationData.containsKey("signalStrength")) {
                locationHistory.setSignalStrength(Integer.valueOf(locationData.get("signalStrength").toString()));
            }
            if (locationData.containsKey("source")) {
                String source = locationData.get("source").toString().toUpperCase();
                try {
                    locationHistory.setLocationSource(LocationHistory.LocationSource.valueOf(source));
                } catch (IllegalArgumentException e) {
                    locationHistory.setLocationSource(LocationHistory.LocationSource.GPS);
                }
            }

            locationHistoryRepository.save(locationHistory);

            // Check geofences
            locationService.checkGeofenceViolations(uav, latitude, longitude, altitude);

            response.put("success", true);
            response.put("message", "Location updated successfully");
            response.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Error updating location: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get current locations of all UAVs
     */
    @GetMapping("/current")
    public ResponseEntity<List<Map<String, Object>>> getCurrentLocations() {
        try {
            List<Map<String, Object>> locations = locationService.getCurrentUAVLocations();
            return ResponseEntity.ok(locations);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get current location of specific UAV
     */
    @GetMapping("/current/{uavId}")
    public ResponseEntity<Map<String, Object>> getCurrentLocation(@PathVariable Integer uavId) {
        try {
            Optional<UAV> uavOpt = uavRepository.findById(uavId);
            if (uavOpt.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
            }

            UAV uav = uavOpt.get();
            Map<String, Object> location = new HashMap<>();
            location.put("uavId", uav.getId());
            location.put("rfidTag", uav.getRfidTag());
            location.put("latitude", uav.getCurrentLatitude());
            location.put("longitude", uav.getCurrentLongitude());
            location.put("altitude", uav.getCurrentAltitudeMeters());
            location.put("lastUpdate", uav.getLastLocationUpdate());
            location.put("hasLocation", uav.hasLocationData());

            return ResponseEntity.ok(location);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get location history for UAV
     */
    @GetMapping("/history/{uavId}")
    public ResponseEntity<List<LocationHistory>> getLocationHistory(
            @PathVariable Integer uavId,
            @RequestParam(defaultValue = "100") Integer limit) {
        try {
            List<LocationHistory> history = locationHistoryRepository.findRecentLocationsByUavId(uavId, limit);
            return ResponseEntity.ok(history);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get location history within time range
     */
    @GetMapping("/history/{uavId}/range")
    public ResponseEntity<List<LocationHistory>> getLocationHistoryRange(
            @PathVariable Integer uavId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            List<LocationHistory> history = locationHistoryRepository.findByUavIdAndTimestampBetween(uavId, startTime, endTime);
            return ResponseEntity.ok(history);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get flight path for UAV
     */
    @GetMapping("/flight-path/{uavId}")
    public ResponseEntity<List<LocationHistory>> getFlightPath(
            @PathVariable Integer uavId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            List<LocationHistory> flightPath = locationHistoryRepository.getFlightPath(uavId, startTime, endTime);
            return ResponseEntity.ok(flightPath);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get UAVs in geographical area
     */
    @GetMapping("/area")
    public ResponseEntity<List<LocationHistory>> getUAVsInArea(
            @RequestParam Double minLatitude,
            @RequestParam Double maxLatitude,
            @RequestParam Double minLongitude,
            @RequestParam Double maxLongitude,
            @RequestParam(defaultValue = "60") Integer minutesBack) {
        try {
            LocalDateTime since = LocalDateTime.now().minusMinutes(minutesBack);
            LocalDateTime now = LocalDateTime.now();
            
            List<LocationHistory> locations = locationHistoryRepository.findLocationsInArea(
                minLatitude, maxLatitude, minLongitude, maxLongitude, since, now);
            
            return ResponseEntity.ok(locations);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get active UAVs (with recent location data)
     */
    @GetMapping("/active")
    public ResponseEntity<List<UAV>> getActiveUAVs(@RequestParam(defaultValue = "30") Integer minutesBack) {
        try {
            LocalDateTime since = LocalDateTime.now().minusMinutes(minutesBack);
            List<UAV> activeUAVs = locationHistoryRepository.findActiveUAVsSince(since);
            return ResponseEntity.ok(activeUAVs);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get location statistics for UAV
     */
    @GetMapping("/stats/{uavId}")
    public ResponseEntity<Map<String, Object>> getLocationStats(
            @PathVariable Integer uavId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            long recordCount = locationHistoryRepository.countByUavIdAndTimestampBetween(uavId, startTime, endTime);
            Double avgSpeed = locationHistoryRepository.getAverageSpeed(uavId, startTime, endTime);
            Double maxAltitude = locationHistoryRepository.getMaxAltitude(uavId, startTime, endTime);
            
            stats.put("recordCount", recordCount);
            stats.put("averageSpeed", avgSpeed);
            stats.put("maxAltitude", maxAltitude);
            stats.put("period", Map.of("start", startTime, "end", endTime));
            
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find UAVs near a point
     */
    @GetMapping("/nearby")
    public ResponseEntity<List<LocationHistory>> findNearbyUAVs(
            @RequestParam Double latitude,
            @RequestParam Double longitude,
            @RequestParam(defaultValue = "5.0") Double radiusKm,
            @RequestParam(defaultValue = "30") Integer minutesBack) {
        try {
            LocalDateTime since = LocalDateTime.now().minusMinutes(minutesBack);
            List<LocationHistory> nearbyLocations = locationHistoryRepository.findLocationsNearPoint(
                latitude, longitude, radiusKm, since);
            return ResponseEntity.ok(nearbyLocations);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Bulk update locations (for multiple UAVs)
     */
    @PostMapping("/bulk-update")
    public ResponseEntity<Map<String, Object>> bulkUpdateLocations(
            @RequestBody List<Map<String, Object>> locationUpdates) {
        
        Map<String, Object> response = new HashMap<>();
        int successCount = 0;
        int errorCount = 0;
        
        try {
            for (Map<String, Object> update : locationUpdates) {
                try {
                    Integer uavId = Integer.valueOf(update.get("uavId").toString());
                    ResponseEntity<Map<String, Object>> result = updateLocation(uavId, update);
                    
                    if (result.getStatusCode() == HttpStatus.OK) {
                        successCount++;
                    } else {
                        errorCount++;
                    }
                } catch (Exception e) {
                    errorCount++;
                }
            }
            
            response.put("success", true);
            response.put("successCount", successCount);
            response.put("errorCount", errorCount);
            response.put("totalProcessed", locationUpdates.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Error processing bulk update: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
